"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Pill,
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp,
  Plus,
  Eye,
  Edit,
  ShoppingCart,
  Calendar,
  Truck,
} from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const medications = [
  {
    id: "MED001",
    name: "Amoxicillin 500mg",
    category: "Antibiotics",
    manufacturer: "PharmaCorp",
    batchNumber: "AMX2024001",
    currentStock: 45,
    minStock: 100,
    maxStock: 500,
    unitPrice: 2.5,
    expiryDate: "2025-06-15",
    location: "A-1-15",
    status: "Low Stock",
    lastRestocked: "2024-01-10",
    supplier: "MedSupply Inc.",
  },
  {
    id: "MED002",
    name: "Paracetamol 500mg",
    category: "Analgesics",
    manufacturer: "HealthPharma",
    batchNumber: "PAR2024002",
    currentStock: 250,
    minStock: 150,
    maxStock: 800,
    unitPrice: 0.75,
    expiryDate: "2025-12-20",
    location: "B-2-08",
    status: "In Stock",
    lastRestocked: "2024-01-15",
    supplier: "Global Meds",
  },
  {
    id: "MED003",
    name: "Insulin Glargine 100IU",
    category: "Diabetes",
    manufacturer: "DiabetesCare",
    batchNumber: "INS2024003",
    currentStock: 25,
    minStock: 30,
    maxStock: 100,
    unitPrice: 45.0,
    expiryDate: "2024-08-30",
    location: "C-1-05",
    status: "Expiring Soon",
    lastRestocked: "2024-01-05",
    supplier: "Specialty Pharma",
  },
  {
    id: "MED004",
    name: "Lisinopril 10mg",
    category: "Cardiovascular",
    manufacturer: "CardioMed",
    batchNumber: "LIS2024004",
    currentStock: 180,
    minStock: 100,
    maxStock: 400,
    unitPrice: 1.25,
    expiryDate: "2025-09-10",
    location: "A-3-12",
    status: "In Stock",
    lastRestocked: "2024-01-20",
    supplier: "MedSupply Inc.",
  },
  {
    id: "MED005",
    name: "Omeprazole 20mg",
    category: "Gastroenterology",
    manufacturer: "GastroPharma",
    batchNumber: "OME2024005",
    currentStock: 0,
    minStock: 75,
    maxStock: 300,
    unitPrice: 3.2,
    expiryDate: "2025-04-25",
    location: "B-1-20",
    status: "Out of Stock",
    lastRestocked: "2023-12-28",
    supplier: "Global Meds",
  },
]

const statusColors = {
  "In Stock": "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  "Low Stock": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  "Out of Stock": "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  "Expiring Soon": "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
}

const stats = [
  {
    title: "Total Medications",
    value: "1,247",
    change: "+12",
    trend: "up",
    icon: Pill,
    color: "text-blue-600 dark:text-blue-400",
  },
  {
    title: "Low Stock Items",
    value: "23",
    change: "+5",
    trend: "up",
    icon: AlertTriangle,
    color: "text-yellow-600 dark:text-yellow-400",
  },
  {
    title: "Out of Stock",
    value: "8",
    change: "-2",
    trend: "down",
    icon: Package,
    color: "text-red-600 dark:text-red-400",
  },
  {
    title: "Expiring Soon",
    value: "15",
    change: "+3",
    trend: "up",
    icon: Calendar,
    color: "text-orange-600 dark:text-orange-400",
  },
]

export default function PharmacyPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("All")
  const [statusFilter, setStatusFilter] = useState("All")
  const [selectedMedication, setSelectedMedication] = useState<(typeof medications)[0] | null>(null)

  const filteredMedications = medications.filter((medication) => {
    const matchesSearch =
      medication.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medication.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      medication.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "All" || medication.category === categoryFilter
    const matchesStatus = statusFilter === "All" || medication.status === statusFilter

    return matchesSearch && matchesCategory && matchesStatus
  })

  const getStockPercentage = (current: number, max: number) => {
    return (current / max) * 100
  }

  const getStockColor = (current: number, min: number, max: number) => {
    const percentage = (current / max) * 100
    if (current === 0) return "bg-red-500"
    if (current <= min) return "bg-yellow-500"
    if (percentage >= 70) return "bg-green-500"
    return "bg-blue-500"
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Pharmacy Management</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage medication inventory, stock levels, and pharmacy operations
              </p>
            </div>
            <div className="flex gap-3 mt-4 sm:mt-0">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700">
                <Truck className="h-4 w-4 mr-2" />
                Orders
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Medication
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                        <div className="flex items-center mt-2">
                          {stat.trend === "up" ? (
                            <TrendingUp className="h-4 w-4 mr-1 text-red-500" />
                          ) : (
                            <TrendingDown className="h-4 w-4 mr-1 text-green-500" />
                          )}
                          <span
                            className={`text-sm font-medium ${stat.trend === "up" ? "text-red-600" : "text-green-600"}`}
                          >
                            {stat.change}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">this week</span>
                        </div>
                      </div>
                      <div className={`p-3 rounded-2xl bg-gray-50 dark:bg-gray-700 ${stat.color}`}>
                        <stat.icon className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search medications by name, ID, or manufacturer..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 dark:border-gray-700"
                  />
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Categories</SelectItem>
                    <SelectItem value="Antibiotics">Antibiotics</SelectItem>
                    <SelectItem value="Analgesics">Analgesics</SelectItem>
                    <SelectItem value="Diabetes">Diabetes</SelectItem>
                    <SelectItem value="Cardiovascular">Cardiovascular</SelectItem>
                    <SelectItem value="Gastroenterology">Gastroenterology</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Status</SelectItem>
                    <SelectItem value="In Stock">In Stock</SelectItem>
                    <SelectItem value="Low Stock">Low Stock</SelectItem>
                    <SelectItem value="Out of Stock">Out of Stock</SelectItem>
                    <SelectItem value="Expiring Soon">Expiring Soon</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Medications Grid */}
          <div className="space-y-4">
            {filteredMedications.map((medication, index) => (
              <motion.div
                key={medication.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row gap-6">
                      {/* Medication Info */}
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{medication.name}</h3>
                            <p className="text-gray-600 dark:text-gray-400">
                              {medication.id} • {medication.manufacturer}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2 mt-2 sm:mt-0">
                            <Badge variant="secondary">{medication.category}</Badge>
                            <Badge className={statusColors[medication.status as keyof typeof statusColors]}>
                              {medication.status}
                            </Badge>
                          </div>
                        </div>

                        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm mb-4">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Current Stock:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{medication.currentStock} units</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Unit Price:</span>
                            <p className="font-medium text-gray-900 dark:text-white">${medication.unitPrice}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Expiry Date:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{medication.expiryDate}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Location:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{medication.location}</p>
                          </div>
                        </div>

                        {/* Stock Level Bar */}
                        <div className="mb-4">
                          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <span>Stock Level</span>
                            <span>
                              {medication.currentStock}/{medication.maxStock} units
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${getStockColor(medication.currentStock, medication.minStock, medication.maxStock)}`}
                              style={{
                                width: `${Math.min(getStockPercentage(medication.currentStock, medication.maxStock), 100)}%`,
                              }}
                            />
                          </div>
                          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-500 mt-1">
                            <span>Min: {medication.minStock}</span>
                            <span>Max: {medication.maxStock}</span>
                          </div>
                        </div>

                        <div className="grid sm:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Batch Number:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{medication.batchNumber}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Last Restocked:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{medication.lastRestocked}</p>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-row lg:flex-col gap-2 lg:w-32">
                        <Button
                          size="sm"
                          onClick={() => setSelectedMedication(medication)}
                          className="bg-blue-600 hover:bg-blue-700 text-white flex-1 lg:flex-none"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-200 dark:border-gray-700 flex-1 lg:flex-none"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        {(medication.status === "Low Stock" || medication.status === "Out of Stock") && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-green-200 text-green-600 hover:bg-green-50 dark:border-green-800 dark:text-green-400 flex-1 lg:flex-none"
                          >
                            <ShoppingCart className="h-4 w-4 mr-1" />
                            Order
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </main>
      </div>

      {/* Medication Detail Modal */}
      {selectedMedication && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{selectedMedication.name}</h2>
                  <p className="text-gray-600 dark:text-gray-400">{selectedMedication.id}</p>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setSelectedMedication(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="text-lg px-4 py-2">
                  {selectedMedication.category}
                </Badge>
                <Badge
                  className={`${statusColors[selectedMedication.status as keyof typeof statusColors]} text-lg px-4 py-2`}
                >
                  {selectedMedication.status}
                </Badge>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Basic Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Manufacturer:</span>
                      <span className="ml-2 font-medium">{selectedMedication.manufacturer}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Batch Number:</span>
                      <span className="ml-2 font-medium">{selectedMedication.batchNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Unit Price:</span>
                      <span className="ml-2 font-medium">${selectedMedication.unitPrice}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Location:</span>
                      <span className="ml-2 font-medium">{selectedMedication.location}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Supplier:</span>
                      <span className="ml-2 font-medium">{selectedMedication.supplier}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Stock Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Current Stock:</span>
                      <span className="ml-2 font-medium">{selectedMedication.currentStock} units</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Minimum Stock:</span>
                      <span className="ml-2 font-medium">{selectedMedication.minStock} units</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Maximum Stock:</span>
                      <span className="ml-2 font-medium">{selectedMedication.maxStock} units</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Expiry Date:</span>
                      <span className="ml-2 font-medium">{selectedMedication.expiryDate}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Last Restocked:</span>
                      <span className="ml-2 font-medium">{selectedMedication.lastRestocked}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Stock Level Visualization */}
              <Card className="border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-lg">Stock Level</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span>Current: {selectedMedication.currentStock} units</span>
                      <span>
                        {getStockPercentage(selectedMedication.currentStock, selectedMedication.maxStock).toFixed(1)}%
                        of capacity
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
                      <div
                        className={`h-4 rounded-full ${getStockColor(selectedMedication.currentStock, selectedMedication.minStock, selectedMedication.maxStock)}`}
                        style={{
                          width: `${Math.min(getStockPercentage(selectedMedication.currentStock, selectedMedication.maxStock), 100)}%`,
                        }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>0</span>
                      <span>Min: {selectedMedication.minStock}</span>
                      <span>Max: {selectedMedication.maxStock}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-4 pt-4">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Medication
                </Button>
                {(selectedMedication.status === "Low Stock" || selectedMedication.status === "Out of Stock") && (
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Reorder Stock
                  </Button>
                )}
                <Button variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  View History
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin', 'doctor', 'lab_technician'), (req, res) => {
  res.json({ success: true, message: 'Get all lab tests - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get lab test - Coming soon' });
});

router.post('/', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Order lab test - Coming soon' });
});

router.put('/:id', authorize('admin', 'doctor', 'lab_technician'), (req, res) => {
  res.json({ success: true, message: 'Update lab test - Coming soon' });
});

router.delete('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Cancel lab test - Coming soon' });
});

router.get('/results/:testId', (req, res) => {
  res.json({ success: true, message: 'Get lab results - Coming soon' });
});

router.post('/results', authorize('admin', 'lab_technician'), (req, res) => {
  res.json({ success: true, message: 'Add lab results - Coming soon' });
});

router.put('/results/:id', authorize('admin', 'lab_technician'), (req, res) => {
  res.json({ success: true, message: 'Update lab results - Coming soon' });
});

router.get('/patient/:patientId', (req, res) => {
  res.json({ success: true, message: 'Get patient lab tests - Coming soon' });
});

module.exports = router;

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get all beds - Coming soon' });
});

router.get('/:id', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get bed - Coming soon' });
});

router.post('/', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Add bed - Coming soon' });
});

router.put('/:id', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Update bed - Coming soon' });
});

router.delete('/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Remove bed - Coming soon' });
});

router.get('/available', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get available beds - Coming soon' });
});

router.post('/:id/assign', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Assign bed - Coming soon' });
});

router.post('/:id/discharge', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Discharge patient - Coming soon' });
});

module.exports = router;

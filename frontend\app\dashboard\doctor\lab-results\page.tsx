"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Eye, Download, TestTube, TrendingUp, AlertTriangle, CheckCircle } from "lucide-react"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { <PERSON><PERSON>ead<PERSON> } from "@/components/doctor-header"

const labResults = [
  {
    id: "LR001",
    patientId: "P001",
    patientName: "<PERSON>",
    patientAge: 45,
    testType: "Complete Blood Count",
    orderDate: "2024-01-18",
    resultDate: "2024-01-20",
    status: "completed",
    priority: "routine",
    results: [
      { parameter: "Hemoglobin", value: "14.2", unit: "g/dL", range: "13.5-17.5", status: "normal" },
      { parameter: "White Blood Cells", value: "7.8", unit: "×10³/μL", range: "4.0-11.0", status: "normal" },
      { parameter: "Platelets", value: "285", unit: "×10³/μL", range: "150-450", status: "normal" },
    ],
    notes: "All parameters within normal limits. Patient's blood count stable.",
  },
  {
    id: "LR002",
    patientId: "P002",
    patientName: "Sarah Johnson",
    patientAge: 32,
    testType: "HbA1c",
    orderDate: "2024-01-17",
    resultDate: "2024-01-19",
    status: "completed",
    priority: "urgent",
    results: [
      { parameter: "HbA1c", value: "7.1", unit: "%", range: "<7.0", status: "high" },
      { parameter: "Glucose (Fasting)", value: "145", unit: "mg/dL", range: "70-100", status: "high" },
    ],
    notes: "HbA1c slightly elevated. Recommend medication adjustment and dietary counseling.",
  },
  {
    id: "LR003",
    patientId: "P003",
    patientName: "Michael Brown",
    patientAge: 58,
    testType: "Lipid Panel",
    orderDate: "2024-01-16",
    resultDate: "2024-01-18",
    status: "completed",
    priority: "routine",
    results: [
      { parameter: "Total Cholesterol", value: "195", unit: "mg/dL", range: "<200", status: "normal" },
      { parameter: "LDL Cholesterol", value: "125", unit: "mg/dL", range: "<100", status: "high" },
      { parameter: "HDL Cholesterol", value: "45", unit: "mg/dL", range: ">40", status: "normal" },
      { parameter: "Triglycerides", value: "150", unit: "mg/dL", range: "<150", status: "normal" },
    ],
    notes: "LDL cholesterol elevated. Consider statin therapy and lifestyle modifications.",
  },
  {
    id: "LR004",
    patientId: "P004",
    patientName: "Emily Davis",
    patientAge: 28,
    testType: "Thyroid Function",
    orderDate: "2024-01-15",
    resultDate: "2024-01-17",
    status: "pending",
    priority: "routine",
    results: [],
    notes: "Results pending from laboratory.",
  },
]

const pendingResults = labResults.filter((result) => result.status === "pending")
const abnormalResults = labResults.filter((result) =>
  result.results.some((r) => r.status === "high" || r.status === "low"),
)

export default function DoctorLabResultsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedResult, setSelectedResult] = useState(null)

  const filteredResults = labResults.filter(
    (result) =>
      result.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.testType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      result.id.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "normal":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "high":
      case "low":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />
      default:
        return <TestTube className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Lab Results</h1>
              <p className="text-gray-600 dark:text-gray-400">Review and analyze patient laboratory test results</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Order Lab Test
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Results</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{labResults.length}</p>
                  </div>
                  <TestTube className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
                    <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{pendingResults.length}</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Abnormal</p>
                    <p className="text-3xl font-bold text-red-600 dark:text-red-400">{abnormalResults.length}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-red-600 dark:text-red-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Normal</p>
                    <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                      {labResults.length - abnormalResults.length - pendingResults.length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="all" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-96">
              <TabsTrigger value="all">All Results ({labResults.length})</TabsTrigger>
              <TabsTrigger value="pending">Pending ({pendingResults.length})</TabsTrigger>
              <TabsTrigger value="abnormal">Abnormal ({abnormalResults.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {/* Search */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search lab results by patient name, test type, or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Results Table */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">All Lab Results</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Complete list of laboratory test results
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Patient</TableHead>
                        <TableHead>Test Type</TableHead>
                        <TableHead>Order Date</TableHead>
                        <TableHead>Result Date</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredResults.map((result) => (
                        <TableRow key={result.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src="/placeholder.svg?height=40&width=40" alt={result.patientName} />
                                <AvatarFallback>
                                  {result.patientName
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">{result.patientName}</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {result.patientAge} years • {result.patientId}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium text-gray-900 dark:text-white">{result.testType}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{result.orderDate}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{result.resultDate || "Pending"}</span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                result.priority === "urgent"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                              }
                            >
                              {result.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                result.status === "completed"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                              }
                            >
                              {result.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl">
                                  <DialogHeader>
                                    <DialogTitle>Lab Results - {result.patientName}</DialogTitle>
                                    <DialogDescription>Detailed laboratory test results and analysis</DialogDescription>
                                  </DialogHeader>
                                  <div className="py-4">
                                    <div className="grid grid-cols-2 gap-6 mb-6">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Test Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Test Type:</span> {result.testType}
                                          </p>
                                          <p>
                                            <span className="font-medium">Order Date:</span> {result.orderDate}
                                          </p>
                                          <p>
                                            <span className="font-medium">Result Date:</span>{" "}
                                            {result.resultDate || "Pending"}
                                          </p>
                                          <p>
                                            <span className="font-medium">Priority:</span> {result.priority}
                                          </p>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Patient Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Name:</span> {result.patientName}
                                          </p>
                                          <p>
                                            <span className="font-medium">Age:</span> {result.patientAge} years
                                          </p>
                                          <p>
                                            <span className="font-medium">Patient ID:</span> {result.patientId}
                                          </p>
                                        </div>
                                      </div>
                                    </div>

                                    {result.results.length > 0 && (
                                      <div className="mb-6">
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-4">Test Results</h4>
                                        <Table>
                                          <TableHeader>
                                            <TableRow>
                                              <TableHead>Parameter</TableHead>
                                              <TableHead>Value</TableHead>
                                              <TableHead>Unit</TableHead>
                                              <TableHead>Reference Range</TableHead>
                                              <TableHead>Status</TableHead>
                                            </TableRow>
                                          </TableHeader>
                                          <TableBody>
                                            {result.results.map((testResult, index) => (
                                              <TableRow key={index}>
                                                <TableCell className="font-medium">{testResult.parameter}</TableCell>
                                                <TableCell>{testResult.value}</TableCell>
                                                <TableCell>{testResult.unit}</TableCell>
                                                <TableCell>{testResult.range}</TableCell>
                                                <TableCell>
                                                  <div className="flex items-center space-x-2">
                                                    {getStatusIcon(testResult.status)}
                                                    <Badge
                                                      variant="secondary"
                                                      className={
                                                        testResult.status === "normal"
                                                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                                          : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                                      }
                                                    >
                                                      {testResult.status}
                                                    </Badge>
                                                  </div>
                                                </TableCell>
                                              </TableRow>
                                            ))}
                                          </TableBody>
                                        </Table>
                                      </div>
                                    )}

                                    <div>
                                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Clinical Notes</h4>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">{result.notes}</p>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                              <Button size="sm" variant="outline">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="pending" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Pending Lab Results</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Tests awaiting results from laboratory
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {pendingResults.map((result, index) => (
                      <div
                        key={result.id}
                        className="p-6 rounded-lg border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                              <TestTube className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">{result.patientName}</h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{result.testType}</p>
                              <p className="text-xs text-orange-600 dark:text-orange-400">
                                Ordered: {result.orderDate}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                              {result.status}
                            </Badge>
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="abnormal" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Abnormal Lab Results</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Results requiring attention or follow-up
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {abnormalResults.map((result, index) => (
                      <div
                        key={result.id}
                        className="p-6 rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">{result.patientName}</h3>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{result.testType}</p>
                              <p className="text-xs text-red-600 dark:text-red-400">
                                {result.results.filter((r) => r.status !== "normal").length} abnormal parameter(s)
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                              Requires Attention
                            </Badge>
                            <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

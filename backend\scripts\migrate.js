const mysql = require('mysql2/promise');
require('dotenv').config();

const createTables = async () => {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || ''
    });

    console.log('Connected to MySQL server');

    // Create database if it doesn't exist
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${process.env.DB_NAME || 'hms_database'}\``);
    console.log(`Database ${process.env.DB_NAME || 'hms_database'} created or already exists`);

    // Use the database
    await connection.execute(`USE \`${process.env.DB_NAME || 'hms_database'}\``);

    // Create tables
    const tables = [
      // Roles table
      `CREATE TABLE IF NOT EXISTS roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        role_id INT NOT NULL,
        status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
        email_verified BOOLEAN DEFAULT FALSE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES roles(id)
      )`,

      // Password resets table
      `CREATE TABLE IF NOT EXISTS password_resets (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_token (user_id, token)
      )`,

      // Departments table
      `CREATE TABLE IF NOT EXISTS departments (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        head_doctor_id INT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      // Doctors table
      `CREATE TABLE IF NOT EXISTS doctors (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT UNIQUE NOT NULL,
        license_number VARCHAR(50) UNIQUE NOT NULL,
        specialization VARCHAR(100) NOT NULL,
        department_id INT,
        experience_years INT DEFAULT 0,
        education TEXT,
        bio TEXT,
        consultation_fee DECIMAL(10,2) DEFAULT 0.00,
        languages JSON,
        availability JSON,
        status ENUM('active', 'inactive', 'on_leave') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id)
      )`,

      // Patients table
      `CREATE TABLE IF NOT EXISTS patients (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT UNIQUE NOT NULL,
        patient_id VARCHAR(20) UNIQUE NOT NULL,
        date_of_birth DATE NOT NULL,
        gender ENUM('male', 'female', 'other') NOT NULL,
        address TEXT NOT NULL,
        emergency_contact_name VARCHAR(100) NOT NULL,
        emergency_contact_phone VARCHAR(20) NOT NULL,
        blood_type ENUM('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'),
        allergies TEXT,
        medical_history TEXT,
        insurance_provider VARCHAR(100),
        insurance_number VARCHAR(50),
        status ENUM('active', 'inactive', 'deleted') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )`,

      // Appointments table
      `CREATE TABLE IF NOT EXISTS appointments (
        id INT PRIMARY KEY AUTO_INCREMENT,
        appointment_id VARCHAR(20) UNIQUE NOT NULL,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        appointment_date DATE NOT NULL,
        appointment_time TIME NOT NULL,
        end_time TIME,
        type ENUM('consultation', 'follow-up', 'emergency', 'check-up') NOT NULL,
        status ENUM('scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'no-show') DEFAULT 'scheduled',
        reason TEXT NOT NULL,
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_id) REFERENCES patients(id),
        FOREIGN KEY (doctor_id) REFERENCES doctors(id),
        FOREIGN KEY (created_by) REFERENCES users(id),
        INDEX idx_appointment_date (appointment_date),
        INDEX idx_doctor_date (doctor_id, appointment_date),
        INDEX idx_patient_date (patient_id, appointment_date)
      )`,

      // Medical records table
      `CREATE TABLE IF NOT EXISTS medical_records (
        id INT PRIMARY KEY AUTO_INCREMENT,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        appointment_id INT,
        visit_date DATE NOT NULL,
        diagnosis TEXT NOT NULL,
        symptoms TEXT,
        treatment TEXT NOT NULL,
        notes TEXT,
        vital_signs JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_id) REFERENCES patients(id),
        FOREIGN KEY (doctor_id) REFERENCES doctors(id),
        FOREIGN KEY (appointment_id) REFERENCES appointments(id)
      )`,

      // Medications table
      `CREATE TABLE IF NOT EXISTS medications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        generic_name VARCHAR(200),
        brand_name VARCHAR(200),
        category VARCHAR(100),
        description TEXT,
        dosage_forms JSON,
        strength VARCHAR(50),
        unit_price DECIMAL(10,2) DEFAULT 0.00,
        stock_quantity INT DEFAULT 0,
        min_stock_level INT DEFAULT 10,
        expiry_date DATE,
        manufacturer VARCHAR(100),
        status ENUM('active', 'inactive', 'discontinued') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )`,

      // Prescriptions table
      `CREATE TABLE IF NOT EXISTS prescriptions (
        id INT PRIMARY KEY AUTO_INCREMENT,
        prescription_id VARCHAR(20) UNIQUE NOT NULL,
        patient_id INT NOT NULL,
        doctor_id INT NOT NULL,
        appointment_id INT,
        prescription_date DATE NOT NULL,
        status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_id) REFERENCES patients(id),
        FOREIGN KEY (doctor_id) REFERENCES doctors(id),
        FOREIGN KEY (appointment_id) REFERENCES appointments(id)
      )`,

      // Prescription items table
      `CREATE TABLE IF NOT EXISTS prescription_items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        prescription_id INT NOT NULL,
        medication_id INT NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        frequency VARCHAR(100) NOT NULL,
        duration VARCHAR(100) NOT NULL,
        quantity INT NOT NULL,
        instructions TEXT,
        dispensed BOOLEAN DEFAULT FALSE,
        dispensed_at TIMESTAMP NULL,
        dispensed_by INT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (prescription_id) REFERENCES prescriptions(id) ON DELETE CASCADE,
        FOREIGN KEY (medication_id) REFERENCES medications(id),
        FOREIGN KEY (dispensed_by) REFERENCES users(id)
      )`
    ];

    // Execute table creation
    for (const table of tables) {
      await connection.execute(table);
    }

    console.log('✅ All tables created successfully');

    // Insert default roles
    const roles = [
      ['admin', 'System Administrator'],
      ['doctor', 'Medical Doctor'],
      ['nurse', 'Registered Nurse'],
      ['patient', 'Patient'],
      ['pharmacist', 'Pharmacist'],
      ['lab_technician', 'Laboratory Technician'],
      ['receptionist', 'Receptionist']
    ];

    for (const [name, description] of roles) {
      await connection.execute(
        'INSERT IGNORE INTO roles (name, description) VALUES (?, ?)',
        [name, description]
      );
    }

    console.log('✅ Default roles inserted');

    // Insert default departments
    const departments = [
      ['Cardiology', 'Heart and cardiovascular system'],
      ['Neurology', 'Brain and nervous system'],
      ['Pediatrics', 'Medical care for infants, children, and adolescents'],
      ['Orthopedics', 'Musculoskeletal system'],
      ['Dermatology', 'Skin, hair, and nails'],
      ['Emergency Medicine', '24/7 emergency care'],
      ['Internal Medicine', 'General adult medicine'],
      ['Radiology', 'Medical imaging'],
      ['Oncology', 'Cancer treatment'],
      ['Psychiatry', 'Mental health']
    ];

    for (const [name, description] of departments) {
      await connection.execute(
        'INSERT IGNORE INTO departments (name, description) VALUES (?, ?)',
        [name, description]
      );
    }

    console.log('✅ Default departments inserted');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run migration
createTables().then(() => {
  console.log('🎉 Database migration completed successfully!');
  process.exit(0);
});

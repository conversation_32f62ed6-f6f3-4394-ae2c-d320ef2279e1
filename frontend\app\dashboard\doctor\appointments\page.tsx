"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Calendar, Clock, Phone, Plus, Eye, Edit, CheckCircle, XCircle } from "lucide-react"
import { motion } from "framer-motion"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/doctor-header"

const todayAppointments = [
  {
    id: "A001",
    time: "09:00 AM",
    patient: "<PERSON>",
    patientId: "P001",
    type: "Consultation",
    duration: "30 min",
    status: "confirmed",
    phone: "+****************",
    notes: "Follow-up for hypertension treatment",
  },
  {
    id: "A002",
    time: "10:30 AM",
    patient: "<PERSON>",
    patientId: "P002",
    type: "Follow-up",
    duration: "20 min",
    status: "confirmed",
    phone: "+****************",
    notes: "Diabetes management review",
  },
  {
    id: "A003",
    time: "11:00 AM",
    patient: "<PERSON>",
    patientId: "P003",
    type: "Check-up",
    duration: "45 min",
    status: "pending",
    phone: "+****************",
    notes: "Annual physical examination",
  },
  {
    id: "A004",
    time: "02:00 PM",
    patient: "Emily Davis",
    patientId: "P004",
    type: "Consultation",
    duration: "30 min",
    status: "confirmed",
    phone: "+****************",
    notes: "Migraine consultation",
  },
  {
    id: "A005",
    time: "03:30 PM",
    patient: "Robert Wilson",
    patientId: "P005",
    type: "Emergency",
    duration: "60 min",
    status: "urgent",
    phone: "+****************",
    notes: "Chest pain evaluation",
  },
]

const upcomingAppointments = [
  {
    id: "A006",
    date: "Tomorrow",
    time: "09:30 AM",
    patient: "Lisa Anderson",
    type: "Follow-up",
    status: "confirmed",
  },
  {
    id: "A007",
    date: "Jan 25",
    time: "11:00 AM",
    patient: "David Miller",
    type: "Consultation",
    status: "confirmed",
  },
  {
    id: "A008",
    date: "Jan 26",
    time: "02:30 PM",
    patient: "Jennifer Taylor",
    type: "Check-up",
    status: "pending",
  },
]

const completedAppointments = [
  {
    id: "A009",
    date: "Yesterday",
    time: "10:00 AM",
    patient: "Mark Thompson",
    type: "Follow-up",
    status: "completed",
    notes: "Prescribed new medication",
  },
  {
    id: "A010",
    date: "Jan 20",
    time: "03:00 PM",
    patient: "Anna Rodriguez",
    type: "Consultation",
    status: "completed",
    notes: "Referred to specialist",
  },
]

export default function DoctorAppointmentsPage() {
  const [selectedAppointment, setSelectedAppointment] = useState(null)

  const handleStatusUpdate = (appointmentId: string, newStatus: string) => {
    // Handle status update logic here
    console.log(`Updating appointment ${appointmentId} to ${newStatus}`)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Appointments</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage your daily schedule and patient appointments</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Schedule Appointment
            </Button>
          </div>

          <Tabs defaultValue="today" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-96">
              <TabsTrigger value="today">Today ({todayAppointments.length})</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming ({upcomingAppointments.length})</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
            </TabsList>

            <TabsContent value="today" className="space-y-6">
              {/* Today's Schedule Overview */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Today</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{todayAppointments.length}</p>
                      </div>
                      <Calendar className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Confirmed</p>
                        <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                          {todayAppointments.filter((a) => a.status === "confirmed").length}
                        </p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Pending</p>
                        <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                          {todayAppointments.filter((a) => a.status === "pending").length}
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Urgent</p>
                        <p className="text-3xl font-bold text-red-600 dark:text-red-400">
                          {todayAppointments.filter((a) => a.status === "urgent").length}
                        </p>
                      </div>
                      <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Today's Appointments */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Today's Schedule</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your appointments for today, {new Date().toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {todayAppointments.map((appointment, index) => (
                      <motion.div
                        key={appointment.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className={`p-6 rounded-lg border transition-all duration-300 hover:shadow-md ${
                          appointment.status === "urgent"
                            ? "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950/20"
                            : appointment.status === "pending"
                              ? "border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20"
                              : "border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-800"
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div
                              className={`w-12 h-12 rounded-full flex items-center justify-center ${
                                appointment.status === "urgent"
                                  ? "bg-red-100 dark:bg-red-900"
                                  : appointment.status === "pending"
                                    ? "bg-orange-100 dark:bg-orange-900"
                                    : "bg-blue-100 dark:bg-blue-900"
                              }`}
                            >
                              <Clock
                                className={`h-5 w-5 ${
                                  appointment.status === "urgent"
                                    ? "text-red-600 dark:text-red-400"
                                    : appointment.status === "pending"
                                      ? "text-orange-600 dark:text-orange-400"
                                      : "text-blue-600 dark:text-blue-400"
                                }`}
                              />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
                                  {appointment.time}
                                </h3>
                                <Badge
                                  variant={
                                    appointment.status === "confirmed"
                                      ? "default"
                                      : appointment.status === "urgent"
                                        ? "destructive"
                                        : "secondary"
                                  }
                                  className={
                                    appointment.status === "confirmed"
                                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                      : appointment.status === "urgent"
                                        ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                        : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                  }
                                >
                                  {appointment.status}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <p className="font-medium text-gray-900 dark:text-white">{appointment.patient}</p>
                                  <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {appointment.type} • {appointment.duration}
                                  </p>
                                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                                    <Phone className="h-3 w-3 mr-1" />
                                    {appointment.phone}
                                  </div>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-600 dark:text-gray-400">
                                    <span className="font-medium">Notes:</span> {appointment.notes}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {appointment.status === "pending" && (
                              <>
                                <Button
                                  size="sm"
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                  onClick={() => handleStatusUpdate(appointment.id, "confirmed")}
                                >
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Confirm
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="text-red-600 border-red-200 hover:bg-red-50 bg-transparent"
                                  onClick={() => handleStatusUpdate(appointment.id, "cancelled")}
                                >
                                  <XCircle className="h-4 w-4 mr-1" />
                                  Cancel
                                </Button>
                              </>
                            )}
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="upcoming" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Upcoming Appointments</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your scheduled appointments for the coming days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingAppointments.map((appointment, index) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{appointment.patient}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {appointment.date} at {appointment.time} • {appointment.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            className={
                              appointment.status === "confirmed"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                            }
                          >
                            {appointment.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="completed" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Completed Appointments</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Recently completed patient appointments
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {completedAppointments.map((appointment, index) => (
                      <div
                        key={appointment.id}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{appointment.patient}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {appointment.date} at {appointment.time} • {appointment.type}
                            </p>
                            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">{appointment.notes}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {appointment.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

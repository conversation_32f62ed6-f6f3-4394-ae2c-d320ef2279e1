const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/vehicles', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get ambulance vehicles - Coming soon' });
});

router.get('/requests', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get ambulance requests - Coming soon' });
});

router.post('/requests', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Create ambulance request - Coming soon' });
});

router.put('/requests/:id', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Update ambulance request - Coming soon' });
});

router.get('/tracking/:id', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Track ambulance - Coming soon' });
});

module.exports = router;

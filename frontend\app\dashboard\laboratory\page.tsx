"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Calendar, Search, Plus, FileText, TestTube, Clock, CheckCircle, AlertCircle } from "lucide-react"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const labTests = [
  {
    id: "LAB001",
    patientName: "<PERSON>",
    patientId: "P001",
    testType: "Blood Test",
    category: "Hematology",
    requestedBy: "Dr. <PERSON>",
    requestDate: "2024-01-15",
    status: "completed",
    priority: "normal",
    results: "Normal values within range",
  },
  {
    id: "LAB002",
    patientName: "Jane Smith",
    patientId: "P002",
    testType: "X-Ray Chest",
    category: "Radiology",
    requestedBy: "Dr. Johnson",
    requestDate: "2024-01-15",
    status: "pending",
    priority: "urgent",
    results: null,
  },
  {
    id: "LAB003",
    patientName: "Mike Wilson",
    patientId: "P003",
    testType: "Urine Analysis",
    category: "Clinical Chemistry",
    requestedBy: "Dr. Brown",
    requestDate: "2024-01-14",
    status: "in-progress",
    priority: "normal",
    results: null,
  },
]

const testCategories = [
  { name: "Hematology", count: 15, icon: TestTube },
  { name: "Clinical Chemistry", count: 23, icon: FileText },
  { name: "Radiology", count: 8, icon: Calendar },
  { name: "Microbiology", count: 12, icon: AlertCircle },
]

export default function LaboratoryPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedTest, setSelectedTest] = useState(null)

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "in-progress":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const filteredTests = labTests.filter((test) => {
    const matchesSearch =
      test.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      test.testType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      test.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || test.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Laboratory Management</h1>
                <p className="text-muted-foreground">Manage lab tests, results, and reports</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="mr-2 h-4 w-4" />
                    New Test Request
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>New Lab Test Request</DialogTitle>
                    <DialogDescription>Create a new laboratory test request</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="patient">Patient</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select patient" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="p001">John Doe (P001)</SelectItem>
                            <SelectItem value="p002">Jane Smith (P002)</SelectItem>
                            <SelectItem value="p003">Mike Wilson (P003)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="doctor">Requesting Doctor</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select doctor" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="dr001">Dr. Smith</SelectItem>
                            <SelectItem value="dr002">Dr. Johnson</SelectItem>
                            <SelectItem value="dr003">Dr. Brown</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="category">Test Category</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="hematology">Hematology</SelectItem>
                            <SelectItem value="chemistry">Clinical Chemistry</SelectItem>
                            <SelectItem value="radiology">Radiology</SelectItem>
                            <SelectItem value="microbiology">Microbiology</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="normal">Normal</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="urgent">Urgent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="testType">Test Type</Label>
                      <Input placeholder="Enter test type" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notes">Clinical Notes</Label>
                      <Textarea placeholder="Enter clinical notes or special instructions" />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">Cancel</Button>
                    <Button className="bg-blue-600 hover:bg-blue-700">Create Request</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {testCategories.map((category) => {
                const Icon = category.icon
                return (
                  <Card key={category.name} className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{category.name}</CardTitle>
                      <Icon className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">{category.count}</div>
                      <p className="text-xs text-muted-foreground">Active tests</p>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <Tabs defaultValue="tests" className="space-y-4">
              <TabsList>
                <TabsTrigger value="tests">Test Requests</TabsTrigger>
                <TabsTrigger value="results">Results</TabsTrigger>
                <TabsTrigger value="reports">Reports</TabsTrigger>
              </TabsList>

              <TabsContent value="tests" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardHeader>
                    <CardTitle>Filter Tests</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search by patient, test type, or ID..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="in-progress">In Progress</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Tests List */}
                <div className="grid gap-4">
                  {filteredTests.map((test) => (
                    <Card key={test.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold">{test.testType}</h3>
                              <Badge className={getPriorityColor(test.priority)}>{test.priority}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Patient: {test.patientName} ({test.patientId})
                            </p>
                            <p className="text-sm text-muted-foreground">
                              Requested by: {test.requestedBy} • {test.requestDate}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getStatusColor(test.status)}>
                              {test.status === "in-progress" ? (
                                <Clock className="mr-1 h-3 w-3" />
                              ) : test.status === "completed" ? (
                                <CheckCircle className="mr-1 h-3 w-3" />
                              ) : (
                                <AlertCircle className="mr-1 h-3 w-3" />
                              )}
                              {test.status}
                            </Badge>
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button variant="outline" size="sm">
                                  View Details
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                  <DialogTitle>Test Details - {test.id}</DialogTitle>
                                  <DialogDescription>{test.testType}</DialogDescription>
                                </DialogHeader>
                                <div className="grid gap-4 py-4">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium">Patient</Label>
                                      <p className="text-sm text-muted-foreground">
                                        {test.patientName} ({test.patientId})
                                      </p>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Requesting Doctor</Label>
                                      <p className="text-sm text-muted-foreground">{test.requestedBy}</p>
                                    </div>
                                  </div>
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <Label className="text-sm font-medium">Category</Label>
                                      <p className="text-sm text-muted-foreground">{test.category}</p>
                                    </div>
                                    <div>
                                      <Label className="text-sm font-medium">Request Date</Label>
                                      <p className="text-sm text-muted-foreground">{test.requestDate}</p>
                                    </div>
                                  </div>
                                  {test.results && (
                                    <div>
                                      <Label className="text-sm font-medium">Results</Label>
                                      <p className="text-sm text-muted-foreground">{test.results}</p>
                                    </div>
                                  )}
                                </div>
                                <div className="flex justify-end space-x-2">
                                  {test.status !== "completed" && (
                                    <Button className="bg-blue-600 hover:bg-blue-700">Update Results</Button>
                                  )}
                                  <Button variant="outline">Print Report</Button>
                                </div>
                              </DialogContent>
                            </Dialog>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="results">
                <Card>
                  <CardHeader>
                    <CardTitle>Test Results</CardTitle>
                    <CardDescription>View and manage completed test results</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Test results management interface would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reports">
                <Card>
                  <CardHeader>
                    <CardTitle>Laboratory Reports</CardTitle>
                    <CardDescription>Generate and view laboratory reports</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Laboratory reports interface would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

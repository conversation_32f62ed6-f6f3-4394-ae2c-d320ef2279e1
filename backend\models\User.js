const { executeQuery } = require('../config/database');
const { hashPassword, comparePassword } = require('../utils/helpers');

class User {
  constructor(data) {
    this.id = data.id;
    this.email = data.email;
    this.password = data.password;
    this.first_name = data.first_name;
    this.last_name = data.last_name;
    this.phone = data.phone;
    this.role_id = data.role_id;
    this.status = data.status || 'active';
    this.email_verified = data.email_verified || false;
    this.last_login = data.last_login;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new user
  static async create(userData) {
    try {
      const hashedPassword = await hashPassword(userData.password);
      
      const query = `
        INSERT INTO users (email, password, first_name, last_name, phone, role_id, status, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      const result = await executeQuery(query, [
        userData.email,
        hashedPassword,
        userData.first_name,
        userData.last_name,
        userData.phone,
        userData.role_id,
        userData.status || 'active'
      ]);

      return result.insertId;
    } catch (error) {
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    try {
      const query = `
        SELECT u.*, r.name as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.id = ?
      `;
      
      const users = await executeQuery(query, [id]);
      return users.length > 0 ? new User(users[0]) : null;
    } catch (error) {
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const query = `
        SELECT u.*, r.name as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.email = ?
      `;
      
      const users = await executeQuery(query, [email]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all users with pagination
  static async findAll(page = 0, limit = 10, filters = {}) {
    try {
      let whereClause = 'WHERE 1=1';
      let params = [];

      if (filters.role) {
        whereClause += ' AND r.name = ?';
        params.push(filters.role);
      }

      if (filters.status) {
        whereClause += ' AND u.status = ?';
        params.push(filters.status);
      }

      if (filters.search) {
        whereClause += ' AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        ${whereClause}
      `;
      
      const countResult = await executeQuery(countQuery, params);
      const total = countResult[0].total;

      // Get users
      const query = `
        SELECT u.*, r.name as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        ${whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, page * limit);
      const users = await executeQuery(query, params);

      return {
        users: users.map(user => new User(user)),
        total,
        page,
        limit
      };
    } catch (error) {
      throw error;
    }
  }

  // Update user
  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && key !== 'id') {
          fields.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      fields.push('updated_at = NOW()');
      params.push(id);

      const query = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
      const result = await executeQuery(query, params);

      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete user (soft delete)
  static async delete(id) {
    try {
      const query = 'UPDATE users SET status = ?, updated_at = NOW() WHERE id = ?';
      const result = await executeQuery(query, ['deleted', id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Update password
  static async updatePassword(id, newPassword) {
    try {
      const hashedPassword = await hashPassword(newPassword);
      const query = 'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?';
      const result = await executeQuery(query, [hashedPassword, id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Verify password
  static async verifyPassword(email, password) {
    try {
      const user = await this.findByEmail(email);
      if (!user) {
        return false;
      }

      return await comparePassword(password, user.password);
    } catch (error) {
      throw error;
    }
  }

  // Update last login
  static async updateLastLogin(id) {
    try {
      const query = 'UPDATE users SET last_login = NOW() WHERE id = ?';
      await executeQuery(query, [id]);
    } catch (error) {
      throw error;
    }
  }

  // Check if email exists
  static async emailExists(email, excludeId = null) {
    try {
      let query = 'SELECT id FROM users WHERE email = ?';
      let params = [email];

      if (excludeId) {
        query += ' AND id != ?';
        params.push(excludeId);
      }

      const result = await executeQuery(query, params);
      return result.length > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get user statistics
  static async getStatistics() {
    try {
      const query = `
        SELECT 
          r.name as role,
          COUNT(*) as count,
          SUM(CASE WHEN u.status = 'active' THEN 1 ELSE 0 END) as active_count,
          SUM(CASE WHEN u.last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_login_count
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        WHERE u.status != 'deleted'
        GROUP BY r.name
      `;

      return await executeQuery(query);
    } catch (error) {
      throw error;
    }
  }

  // Get user profile with additional info
  static async getProfile(id) {
    try {
      const query = `
        SELECT 
          u.*,
          r.name as role_name,
          CASE 
            WHEN r.name = 'patient' THEN p.patient_id
            WHEN r.name = 'doctor' THEN d.license_number
            ELSE NULL
          END as profile_identifier
        FROM users u
        LEFT JOIN roles r ON u.role_id = r.id
        LEFT JOIN patients p ON u.id = p.user_id
        LEFT JOIN doctors d ON u.id = d.user_id
        WHERE u.id = ? AND u.status = 'active'
      `;

      const users = await executeQuery(query, [id]);
      return users.length > 0 ? users[0] : null;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = User;

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/cases', authorize('admin', 'doctor', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get emergency cases - Coming soon' });
});

router.get('/cases/:id', authorize('admin', 'doctor', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get emergency case - Coming soon' });
});

router.post('/cases', authorize('admin', 'doctor', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Create emergency case - Coming soon' });
});

router.put('/cases/:id', authorize('admin', 'doctor', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Update emergency case - Coming soon' });
});

router.get('/stats', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get emergency statistics - Coming soon' });
});

module.exports = router;

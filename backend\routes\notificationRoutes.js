const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', (req, res) => {
  res.json({ success: true, message: 'Get user notifications - Coming soon' });
});

router.post('/', (req, res) => {
  res.json({ success: true, message: 'Create notification - Coming soon' });
});

router.put('/:id/read', (req, res) => {
  res.json({ success: true, message: 'Mark notification as read - Coming soon' });
});

router.delete('/:id', (req, res) => {
  res.json({ success: true, message: 'Delete notification - Coming soon' });
});

module.exports = router;

const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Protect routes - verify JWT token
const protect = async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      // Get token from header
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Get user from database
      const query = `
        SELECT u.*, r.name as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.id = ? AND u.status = 'active'
      `;
      
      const users = await executeQuery(query, [decoded.id]);
      
      if (users.length === 0) {
        return res.status(401).json({
          success: false,
          message: 'Not authorized, user not found'
        });
      }

      req.user = users[0];
      next();
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(401).json({
        success: false,
        message: 'Not authorized, token failed'
      });
    }
  }

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized, no token'
    });
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized'
      });
    }

    if (!roles.includes(req.user.role_name)) {
      return res.status(403).json({
        success: false,
        message: `User role ${req.user.role_name} is not authorized to access this route`
      });
    }
    next();
  };
};

// Check if user owns the resource or is admin
const authorizeOwnerOrAdmin = (resourceUserIdField = 'user_id') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Not authorized'
        });
      }

      // Admin can access everything
      if (req.user.role_name === 'admin') {
        return next();
      }

      // For patients, check if they're accessing their own data
      if (req.user.role_name === 'patient') {
        const patientQuery = 'SELECT id FROM patients WHERE user_id = ?';
        const patients = await executeQuery(patientQuery, [req.user.id]);
        
        if (patients.length > 0) {
          req.user.patient_id = patients[0].id;
          
          // Check if the requested resource belongs to this patient
          if (req.params.patientId && req.params.patientId != patients[0].id) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this resource'
            });
          }
        }
      }

      // For doctors, check if they're accessing their own data or their patients' data
      if (req.user.role_name === 'doctor') {
        const doctorQuery = 'SELECT id FROM doctors WHERE user_id = ?';
        const doctors = await executeQuery(doctorQuery, [req.user.id]);
        
        if (doctors.length > 0) {
          req.user.doctor_id = doctors[0].id;
          
          // Check if the requested resource belongs to this doctor
          if (req.params.doctorId && req.params.doctorId != doctors[0].id) {
            return res.status(403).json({
              success: false,
              message: 'Not authorized to access this resource'
            });
          }
        }
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      return res.status(500).json({
        success: false,
        message: 'Server error during authorization'
      });
    }
  };
};

// Optional auth - doesn't require token but adds user if present
const optionalAuth = async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    try {
      token = req.headers.authorization.split(' ')[1];
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      const query = `
        SELECT u.*, r.name as role_name 
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.id = ? AND u.status = 'active'
      `;
      
      const users = await executeQuery(query, [decoded.id]);
      
      if (users.length > 0) {
        req.user = users[0];
      }
    } catch (error) {
      // Token is invalid but we continue without user
      console.log('Optional auth failed:', error.message);
    }
  }

  next();
};

module.exports = {
  protect,
  authorize,
  authorizeOwnerOrAdmin,
  optionalAuth
};

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin', 'receptionist'), (req, res) => {
  res.json({ success: true, message: 'Get all bills - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get bill - Coming soon' });
});

router.post('/', authorize('admin', 'receptionist'), (req, res) => {
  res.json({ success: true, message: 'Create bill - Coming soon' });
});

router.put('/:id', authorize('admin', 'receptionist'), (req, res) => {
  res.json({ success: true, message: 'Update bill - Coming soon' });
});

router.delete('/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Delete bill - Coming soon' });
});

router.get('/patient/:patientId', (req, res) => {
  res.json({ success: true, message: 'Get patient bills - Coming soon' });
});

router.post('/:id/payment', authorize('admin', 'receptionist'), (req, res) => {
  res.json({ success: true, message: 'Process payment - Coming soon' });
});

router.get('/reports', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Get billing reports - Coming soon' });
});

module.exports = router;

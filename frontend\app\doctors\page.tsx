"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, MapPin, Clock, Star, Calendar, Phone, Award, GraduationCap, Users } from "lucide-react"
import { motion } from "framer-motion"
import { Navigation } from "@/components/navigation"
import Link from "next/link"

const doctors = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    specialty: "Cardiologist",
    department: "Cardiology",
    experience: "15+ years",
    education: "MD, Harvard Medical School",
    rating: 4.9,
    reviews: 127,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Specialized in interventional cardiology and heart disease prevention with extensive experience in cardiac catheterization.",
    languages: ["English", "Spanish"],
    availability: "Mon-Fri: 9AM-5PM",
    location: "Cardiology Wing, 2nd Floor",
    achievements: ["Board Certified Cardiologist", "Fellow of American College of Cardiology"],
  },
  {
    id: 2,
    name: "Dr. <PERSON>",
    specialty: "Neurologist",
    department: "Neurology",
    experience: "12+ years",
    education: "MD, Johns Hopkins University",
    rating: 4.8,
    reviews: 89,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Expert in stroke care, epilepsy treatment, and neurological disorders with focus on patient-centered care.",
    languages: ["English", "Mandarin"],
    availability: "Mon-Thu: 8AM-4PM",
    location: "Neurology Department, 3rd Floor",
    achievements: ["Stroke Specialist Certification", "Epilepsy Foundation Award"],
  },
  {
    id: 3,
    name: "Dr. Emily Rodriguez",
    specialty: "Pediatrician",
    department: "Pediatrics",
    experience: "10+ years",
    education: "MD, Stanford University",
    rating: 4.9,
    reviews: 156,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Dedicated to providing comprehensive care for children from infancy through adolescence with compassionate approach.",
    languages: ["English", "Spanish", "Portuguese"],
    availability: "Mon-Sat: 8AM-6PM",
    location: "Pediatrics Wing, 1st Floor",
    achievements: ["Board Certified Pediatrician", "Child Advocacy Award"],
  },
  {
    id: 4,
    name: "Dr. James Thompson",
    specialty: "Orthopedic Surgeon",
    department: "Orthopedics",
    experience: "18+ years",
    education: "MD, Mayo Clinic",
    rating: 4.7,
    reviews: 203,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Specializes in joint replacement surgery, sports medicine, and minimally invasive orthopedic procedures.",
    languages: ["English"],
    availability: "Tue-Fri: 7AM-3PM",
    location: "Orthopedic Center, Ground Floor",
    achievements: ["Fellowship in Joint Replacement", "Sports Medicine Certification"],
  },
  {
    id: 5,
    name: "Dr. Lisa Park",
    specialty: "Dermatologist",
    department: "Dermatology",
    experience: "8+ years",
    education: "MD, UCLA Medical School",
    rating: 4.8,
    reviews: 94,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Expert in medical and cosmetic dermatology, skin cancer treatment, and advanced dermatological procedures.",
    languages: ["English", "Korean"],
    availability: "Mon-Wed-Fri: 9AM-5PM",
    location: "Dermatology Clinic, 2nd Floor",
    achievements: ["Dermatology Board Certification", "Mohs Surgery Fellowship"],
  },
  {
    id: 6,
    name: "Dr. Robert Johnson",
    specialty: "Emergency Medicine",
    department: "Emergency",
    experience: "14+ years",
    education: "MD, University of Pennsylvania",
    rating: 4.6,
    reviews: 78,
    image: "/placeholder.svg?height=300&width=300",
    bio: "Emergency medicine specialist with expertise in trauma care, critical care, and emergency procedures.",
    languages: ["English"],
    availability: "24/7 Emergency Coverage",
    location: "Emergency Department",
    achievements: ["Emergency Medicine Board Certification", "Trauma Care Specialist"],
  },
]

const specialties = [
  "All Specialties",
  "Cardiology",
  "Neurology",
  "Pediatrics",
  "Orthopedics",
  "Dermatology",
  "Emergency Medicine",
  "Internal Medicine",
  "Oncology",
]

export default function DoctorsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedSpecialty, setSelectedSpecialty] = useState("All Specialties")
  const [filteredDoctors, setFilteredDoctors] = useState(doctors)

  const handleSearch = () => {
    let filtered = doctors

    if (searchTerm) {
      filtered = filtered.filter(
        (doctor) =>
          doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          doctor.specialty.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (selectedSpecialty !== "All Specialties") {
      filtered = filtered.filter((doctor) => doctor.department === selectedSpecialty)
    }

    setFilteredDoctors(filtered)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-4 py-2 mb-4">
              Our Medical Team
            </Badge>
            <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Meet Our <span className="text-blue-600 dark:text-blue-400">Expert Doctors</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Our team of experienced physicians and specialists are dedicated to providing you with the highest quality
              medical care.
            </p>
          </motion.div>

          {/* Search and Filter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="max-w-4xl mx-auto"
          >
            <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by doctor name or specialty..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
                    />
                  </div>
                  <Select value={selectedSpecialty} onValueChange={setSelectedSpecialty}>
                    <SelectTrigger className="w-full md:w-48 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400">
                      <SelectValue placeholder="Select specialty" />
                    </SelectTrigger>
                    <SelectContent>
                      {specialties.map((specialty) => (
                        <SelectItem key={specialty} value={specialty}>
                          {specialty}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Doctors Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredDoctors.map((doctor, index) => (
              <motion.div
                key={doctor.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm overflow-hidden group">
                  <div className="relative">
                    <img
                      src={doctor.image || "/placeholder.svg"}
                      alt={doctor.name}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-blue-600 text-white">{doctor.specialty}</Badge>
                    </div>
                  </div>

                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">{doctor.name}</h3>
                        <p className="text-blue-600 dark:text-blue-400 font-medium">{doctor.specialty}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{doctor.department} Department</p>
                      </div>

                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center">
                          <GraduationCap className="h-4 w-4 mr-1" />
                          {doctor.experience}
                        </div>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 mr-1 text-yellow-400 fill-current" />
                          {doctor.rating} ({doctor.reviews})
                        </div>
                      </div>

                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">{doctor.bio}</p>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center text-gray-600 dark:text-gray-400">
                          <Clock className="h-4 w-4 mr-2" />
                          {doctor.availability}
                        </div>
                        <div className="flex items-center text-gray-600 dark:text-gray-400">
                          <MapPin className="h-4 w-4 mr-2" />
                          {doctor.location}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">Education:</div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{doctor.education}</p>
                      </div>

                      <div className="space-y-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">Languages:</div>
                        <div className="flex flex-wrap gap-1">
                          {doctor.languages.map((language) => (
                            <Badge key={language} variant="secondary" className="text-xs">
                              {language}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">Achievements:</div>
                        <ul className="space-y-1">
                          {doctor.achievements.map((achievement, idx) => (
                            <li key={idx} className="text-xs text-gray-600 dark:text-gray-400 flex items-center">
                              <Award className="h-3 w-3 mr-1 text-blue-600 dark:text-blue-400" />
                              {achievement}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex gap-2 pt-4 border-t border-gray-100 dark:border-gray-700">
                        <Button asChild size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700 text-white">
                          <Link href="/appointments">
                            <Calendar className="h-4 w-4 mr-1" />
                            Book Appointment
                          </Link>
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-200 text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:text-gray-400"
                        >
                          <Phone className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {filteredDoctors.length === 0 && (
            <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No doctors found</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search criteria or browse all specialties.
              </p>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <h2 className="text-4xl font-bold mb-4">Ready to Schedule Your Appointment?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Our experienced doctors are here to provide you with personalized, compassionate care
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-blue-50">
                <Link href="/appointments">
                  <Calendar className="mr-2 h-5 w-5" />
                  Book Appointment
                </Link>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                <Link href="/contact">
                  <Phone className="mr-2 h-5 w-5" />
                  Contact Us
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

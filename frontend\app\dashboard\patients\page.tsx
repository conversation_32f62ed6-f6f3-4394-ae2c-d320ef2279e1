"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Plus,
  Filter,
  Download,
  Eye,
  Edit,
  UserCheck,
  Calendar,
  Phone,
  Mail,
  MapPin,
  FileText,
  AlertCircle,
} from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

const patients = [
  {
    id: "P001",
    name: "<PERSON>",
    age: 45,
    gender: "Male",
    phone: "******-567-8901",
    email: "<EMAIL>",
    address: "123 Main St, City, State 12345",
    bloodType: "O+",
    lastVisit: "2024-01-15",
    nextAppointment: "2024-01-25",
    status: "Active",
    department: "Cardiology",
    doctor: "Dr. Sarah Williams",
    insurance: "Blue Cross Blue Shield",
    emergencyContact: "Jane Smith - ******-567-8902",
    medicalHistory: ["Hypertension", "Diabetes Type 2"],
    allergies: ["Penicillin"],
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "P002",
    name: "Sarah Johnson",
    age: 32,
    gender: "Female",
    phone: "******-567-8903",
    email: "<EMAIL>",
    address: "456 Oak Ave, City, State 12345",
    bloodType: "A-",
    lastVisit: "2024-01-18",
    nextAppointment: "2024-01-28",
    status: "Active",
    department: "Pediatrics",
    doctor: "Dr. Emily Rodriguez",
    insurance: "Aetna",
    emergencyContact: "Mike Johnson - ******-567-8904",
    medicalHistory: ["Asthma"],
    allergies: ["Shellfish", "Latex"],
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "P003",
    name: "Michael Brown",
    age: 58,
    gender: "Male",
    phone: "******-567-8905",
    email: "<EMAIL>",
    address: "789 Pine St, City, State 12345",
    bloodType: "B+",
    lastVisit: "2024-01-20",
    nextAppointment: null,
    status: "Discharged",
    department: "Orthopedics",
    doctor: "Dr. James Thompson",
    insurance: "Medicare",
    emergencyContact: "Lisa Brown - ******-567-8906",
    medicalHistory: ["Arthritis", "High Cholesterol"],
    allergies: ["None"],
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "P004",
    name: "Emily Davis",
    age: 28,
    gender: "Female",
    phone: "******-567-8907",
    email: "<EMAIL>",
    address: "321 Elm St, City, State 12345",
    bloodType: "AB+",
    lastVisit: "2024-01-22",
    nextAppointment: "2024-01-30",
    status: "Active",
    department: "Dermatology",
    doctor: "Dr. Lisa Park",
    insurance: "Cigna",
    emergencyContact: "David Davis - ******-567-8908",
    medicalHistory: ["Eczema"],
    allergies: ["Sulfa drugs"],
    image: "/placeholder.svg?height=100&width=100",
  },
  {
    id: "P005",
    name: "Robert Wilson",
    age: 67,
    gender: "Male",
    phone: "******-567-8909",
    email: "<EMAIL>",
    address: "654 Maple Dr, City, State 12345",
    bloodType: "O-",
    lastVisit: "2024-01-19",
    nextAppointment: "2024-01-26",
    status: "Critical",
    department: "Neurology",
    doctor: "Dr. Michael Chen",
    insurance: "United Healthcare",
    emergencyContact: "Mary Wilson - ******-567-8910",
    medicalHistory: ["Stroke", "Hypertension", "Atrial Fibrillation"],
    allergies: ["Aspirin"],
    image: "/placeholder.svg?height=100&width=100",
  },
]

const statusColors = {
  Active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Critical: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  Discharged: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
  Admitted: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
}

export default function PatientsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [departmentFilter, setDepartmentFilter] = useState("All")
  const [selectedPatient, setSelectedPatient] = useState<(typeof patients)[0] | null>(null)

  const [showAddPatient, setShowAddPatient] = useState(false)
  const [showEditPatient, setShowEditPatient] = useState(false)
  const [showBookAppointment, setShowBookAppointment] = useState(false)
  const [editingPatient, setEditingPatient] = useState<(typeof patients)[0] | null>(null)

  const filteredPatients = patients.filter((patient) => {
    const matchesSearch =
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "All" || patient.status === statusFilter
    const matchesDepartment = departmentFilter === "All" || patient.department === departmentFilter

    return matchesSearch && matchesStatus && matchesDepartment
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Patient Management</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage patient records, appointments, and medical information
              </p>
            </div>
            <div className="flex gap-3 mt-4 sm:mt-0">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700 bg-transparent">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Dialog open={showAddPatient} onOpenChange={setShowAddPatient}>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Patient
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Patient</DialogTitle>
                    <DialogDescription>Create a new patient record</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="patientName">Full Name</Label>
                        <Input id="patientName" placeholder="John Doe" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="patientAge">Age</Label>
                        <Input id="patientAge" type="number" placeholder="30" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="patientGender">Gender</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">Male</SelectItem>
                            <SelectItem value="female">Female</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bloodType">Blood Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select blood type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="A+">A+</SelectItem>
                            <SelectItem value="A-">A-</SelectItem>
                            <SelectItem value="B+">B+</SelectItem>
                            <SelectItem value="B-">B-</SelectItem>
                            <SelectItem value="AB+">AB+</SelectItem>
                            <SelectItem value="AB-">AB-</SelectItem>
                            <SelectItem value="O+">O+</SelectItem>
                            <SelectItem value="O-">O-</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="patientPhone">Phone</Label>
                        <Input id="patientPhone" placeholder="******-567-8901" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="patientEmail">Email</Label>
                        <Input id="patientEmail" type="email" placeholder="<EMAIL>" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="patientAddress">Address</Label>
                      <Textarea id="patientAddress" placeholder="123 Main St, City, State 12345" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="emergencyContact">Emergency Contact</Label>
                      <Input id="emergencyContact" placeholder="Contact Name - Phone Number" />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowAddPatient(false)}>
                      Cancel
                    </Button>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() => {
                        // Add patient logic here
                        setShowAddPatient(false)
                      }}
                    >
                      Add Patient
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Filters */}
          <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search patients by name or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 dark:border-gray-700"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Status</SelectItem>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                    <SelectItem value="Discharged">Discharged</SelectItem>
                    <SelectItem value="Admitted">Admitted</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Departments</SelectItem>
                    <SelectItem value="Cardiology">Cardiology</SelectItem>
                    <SelectItem value="Neurology">Neurology</SelectItem>
                    <SelectItem value="Pediatrics">Pediatrics</SelectItem>
                    <SelectItem value="Orthopedics">Orthopedics</SelectItem>
                    <SelectItem value="Dermatology">Dermatology</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" className="border-gray-200 dark:border-gray-700 bg-transparent">
                  <Filter className="h-4 w-4 mr-2" />
                  More Filters
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Patients Grid */}
          <div className="grid gap-6">
            {filteredPatients.map((patient, index) => (
              <motion.div
                key={patient.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row gap-6">
                      {/* Patient Photo & Basic Info */}
                      <div className="flex items-center space-x-4">
                        <img
                          src={patient.image || "/placeholder.svg"}
                          alt={patient.name}
                          className="w-16 h-16 rounded-full object-cover border-2 border-gray-200 dark:border-gray-700"
                        />
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">{patient.name}</h3>
                          <p className="text-gray-600 dark:text-gray-400">
                            {patient.id} • {patient.age} years • {patient.gender}
                          </p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge className={statusColors[patient.status as keyof typeof statusColors]}>
                              {patient.status}
                            </Badge>
                            {patient.status === "Critical" && <AlertCircle className="h-4 w-4 text-red-500" />}
                          </div>
                        </div>
                      </div>

                      {/* Patient Details */}
                      <div className="flex-1 grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-3">
                          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <Phone className="h-4 w-4 mr-2" />
                            {patient.phone}
                          </div>
                          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <Mail className="h-4 w-4 mr-2" />
                            {patient.email}
                          </div>
                          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span className="truncate">{patient.address}</span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Department:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">{patient.department}</span>
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Doctor:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">{patient.doctor}</span>
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Blood Type:</span>
                            <span className="ml-2 font-medium text-red-600 dark:text-red-400">{patient.bloodType}</span>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Last Visit:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">{patient.lastVisit}</span>
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Next Appointment:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">
                              {patient.nextAppointment || "Not scheduled"}
                            </span>
                          </div>
                          <div className="text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Insurance:</span>
                            <span className="ml-2 font-medium text-gray-900 dark:text-white">{patient.insurance}</span>
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-col gap-2 lg:w-32">
                        <Button
                          size="sm"
                          onClick={() => setSelectedPatient(patient)}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-200 dark:border-gray-700 bg-transparent"
                          onClick={() => {
                            setEditingPatient(patient)
                            setShowEditPatient(true)
                          }}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-green-200 text-green-600 hover:bg-green-50 dark:border-green-800 dark:text-green-400 bg-transparent"
                          onClick={() => {
                            setEditingPatient(patient)
                            setShowBookAppointment(true)
                          }}
                        >
                          <Calendar className="h-4 w-4 mr-1" />
                          Book
                        </Button>
                      </div>
                    </div>

                    {/* Medical Info */}
                    <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700">
                      <div className="grid md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">Medical History:</span>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {patient.medicalHistory.map((condition, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                {condition}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">Allergies:</span>
                          <div className="mt-1 flex flex-wrap gap-1">
                            {patient.allergies.map((allergy, idx) => (
                              <Badge
                                key={idx}
                                variant="secondary"
                                className={`text-xs ${
                                  allergy === "None"
                                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                }`}
                              >
                                {allergy}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {filteredPatients.length === 0 && (
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-12 text-center">
                <UserCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No patients found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search criteria or add a new patient.
                </p>
              </CardContent>
            </Card>
          )}
        </main>
      </div>

      {/* Patient Detail Modal */}
      {selectedPatient && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <img
                    src={selectedPatient.image || "/placeholder.svg"}
                    alt={selectedPatient.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">{selectedPatient.name}</h2>
                    <p className="text-gray-600 dark:text-gray-400">
                      {selectedPatient.id} • {selectedPatient.age} years • {selectedPatient.gender}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setSelectedPatient(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-3 text-gray-400" />
                      <span>{selectedPatient.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-3 text-gray-400" />
                      <span>{selectedPatient.email}</span>
                    </div>
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 mr-3 text-gray-400 mt-0.5" />
                      <span>{selectedPatient.address}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Medical Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Blood Type:</span>
                      <span className="ml-2 font-medium text-red-600 dark:text-red-400">
                        {selectedPatient.bloodType}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Department:</span>
                      <span className="ml-2 font-medium">{selectedPatient.department}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Attending Doctor:</span>
                      <span className="ml-2 font-medium">{selectedPatient.doctor}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Insurance:</span>
                      <span className="ml-2 font-medium">{selectedPatient.insurance}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-lg">Emergency Contact</CardTitle>
                </CardHeader>
                <CardContent>
                  <p>{selectedPatient.emergencyContact}</p>
                </CardContent>
              </Card>

              <div className="grid md:grid-cols-2 gap-6">
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Medical History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedPatient.medicalHistory.map((condition, idx) => (
                        <Badge key={idx} variant="secondary">
                          {condition}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg">Allergies</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedPatient.allergies.map((allergy, idx) => (
                        <Badge
                          key={idx}
                          className={
                            allergy === "None"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                          }
                        >
                          {allergy}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="flex gap-4 pt-4">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Patient
                </Button>
                <Button variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  View Records
                </Button>
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Appointment
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Edit Patient Modal */}
      {showEditPatient && editingPatient && (
        <Dialog open={showEditPatient} onOpenChange={setShowEditPatient}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Patient - {editingPatient.name}</DialogTitle>
              <DialogDescription>Update patient information</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editName">Full Name</Label>
                  <Input id="editName" defaultValue={editingPatient.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editAge">Age</Label>
                  <Input id="editAge" type="number" defaultValue={editingPatient.age} />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editPhone">Phone</Label>
                  <Input id="editPhone" defaultValue={editingPatient.phone} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editEmail">Email</Label>
                  <Input id="editEmail" type="email" defaultValue={editingPatient.email} />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="editAddress">Address</Label>
                <Textarea id="editAddress" defaultValue={editingPatient.address} />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditPatient(false)}>
                Cancel
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  // Update patient logic here
                  setShowEditPatient(false)
                }}
              >
                Update Patient
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Book Appointment Modal */}
      {showBookAppointment && editingPatient && (
        <Dialog open={showBookAppointment} onOpenChange={setShowBookAppointment}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Book Appointment - {editingPatient.name}</DialogTitle>
              <DialogDescription>Schedule a new appointment for this patient</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appointmentDate">Date</Label>
                  <Input id="appointmentDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="appointmentTime">Time</Label>
                  <Input id="appointmentTime" type="time" />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="appointmentDoctor">Doctor</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select doctor" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="dr1">Dr. Sarah Williams</SelectItem>
                      <SelectItem value="dr2">Dr. Emily Rodriguez</SelectItem>
                      <SelectItem value="dr3">Dr. James Thompson</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="appointmentType">Appointment Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="consultation">Consultation</SelectItem>
                      <SelectItem value="followup">Follow-up</SelectItem>
                      <SelectItem value="emergency">Emergency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="appointmentReason">Reason</Label>
                <Textarea id="appointmentReason" placeholder="Reason for appointment" />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowBookAppointment(false)}>
                Cancel
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  // Book appointment logic here
                  setShowBookAppointment(false)
                }}
              >
                Book Appointment
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

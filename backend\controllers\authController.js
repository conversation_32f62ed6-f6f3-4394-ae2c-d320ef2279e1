const User = require('../models/User');
const Patient = require('../models/Patient');
const { executeQuery } = require('../config/database');
const { 
  generateToken, 
  generateRefreshToken, 
  generateRandomToken,
  formatResponse,
  hashPassword
} = require('../utils/helpers');
const { sendWelcomeEmail, sendPasswordResetEmail } = require('../utils/emailService');

// @desc    Register user
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res, next) => {
  try {
    const { email, password, first_name, last_name, phone, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json(
        formatResponse(false, 'User with this email already exists')
      );
    }

    // Get role ID
    const roleQuery = 'SELECT id FROM roles WHERE name = ?';
    const roles = await executeQuery(roleQuery, [role]);
    
    if (roles.length === 0) {
      return res.status(400).json(
        formatResponse(false, 'Invalid role specified')
      );
    }

    // Create user
    const userId = await User.create({
      email,
      password,
      first_name,
      last_name,
      phone,
      role_id: roles[0].id
    });

    // If registering as patient, create patient record
    let patientData = null;
    if (role === 'patient') {
      const { date_of_birth, gender, address, emergency_contact_name, emergency_contact_phone } = req.body;
      
      if (!date_of_birth || !gender || !address || !emergency_contact_name || !emergency_contact_phone) {
        return res.status(400).json(
          formatResponse(false, 'Additional patient information required')
        );
      }

      const patientResult = await Patient.create({
        user_id: userId,
        date_of_birth,
        gender,
        address,
        emergency_contact_name,
        emergency_contact_phone,
        blood_type: req.body.blood_type,
        allergies: req.body.allergies,
        medical_history: req.body.medical_history,
        insurance_provider: req.body.insurance_provider,
        insurance_number: req.body.insurance_number
      });

      patientData = {
        patient_id: patientResult.patient_id
      };

      // Send welcome email
      try {
        await sendWelcomeEmail(email, `${first_name} ${last_name}`, patientResult.patient_id);
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
      }
    }

    // Generate tokens
    const token = generateToken({ id: userId, role });
    const refreshToken = generateRefreshToken({ id: userId });

    res.status(201).json(
      formatResponse(true, 'User registered successfully', {
        user: {
          id: userId,
          email,
          first_name,
          last_name,
          phone,
          role
        },
        patient: patientData,
        token,
        refreshToken
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Check if user exists and password is correct
    const user = await User.findByEmail(email);
    if (!user || !(await User.verifyPassword(email, password))) {
      return res.status(401).json(
        formatResponse(false, 'Invalid email or password')
      );
    }

    // Check if user is active
    if (user.status !== 'active') {
      return res.status(401).json(
        formatResponse(false, 'Account is not active')
      );
    }

    // Update last login
    await User.updateLastLogin(user.id);

    // Get additional profile info based on role
    let profileData = null;
    if (user.role_name === 'patient') {
      profileData = await Patient.findByUserId(user.id);
    } else if (user.role_name === 'doctor') {
      const doctorQuery = 'SELECT * FROM doctors WHERE user_id = ?';
      const doctors = await executeQuery(doctorQuery, [user.id]);
      profileData = doctors.length > 0 ? doctors[0] : null;
    }

    // Generate tokens
    const token = generateToken({ id: user.id, role: user.role_name });
    const refreshToken = generateRefreshToken({ id: user.id });

    res.json(
      formatResponse(true, 'Login successful', {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          role: user.role_name,
          last_login: user.last_login
        },
        profile: profileData,
        token,
        refreshToken
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get current user
// @route   GET /api/auth/me
// @access  Private
const getMe = async (req, res, next) => {
  try {
    const user = await User.getProfile(req.user.id);
    
    if (!user) {
      return res.status(404).json(
        formatResponse(false, 'User not found')
      );
    }

    res.json(
      formatResponse(true, 'User profile retrieved', {
        user: {
          id: user.id,
          email: user.email,
          first_name: user.first_name,
          last_name: user.last_name,
          phone: user.phone,
          role: user.role_name,
          profile_identifier: user.profile_identifier,
          email_verified: user.email_verified,
          last_login: user.last_login,
          created_at: user.created_at
        }
      })
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Refresh token
// @route   POST /api/auth/refresh
// @access  Public
const refreshToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(401).json(
        formatResponse(false, 'Refresh token required')
      );
    }

    // Verify refresh token
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

    // Get user
    const user = await User.findById(decoded.id);
    if (!user || user.status !== 'active') {
      return res.status(401).json(
        formatResponse(false, 'Invalid refresh token')
      );
    }

    // Generate new tokens
    const newToken = generateToken({ id: user.id, role: user.role_name });
    const newRefreshToken = generateRefreshToken({ id: user.id });

    res.json(
      formatResponse(true, 'Token refreshed successfully', {
        token: newToken,
        refreshToken: newRefreshToken
      })
    );
  } catch (error) {
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json(
        formatResponse(false, 'Invalid refresh token')
      );
    }
    next(error);
  }
};

// @desc    Forgot password
// @route   POST /api/auth/forgot-password
// @access  Public
const forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(404).json(
        formatResponse(false, 'User not found')
      );
    }

    // Generate reset token
    const resetToken = generateRandomToken();
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour

    // Save reset token to database
    const query = `
      INSERT INTO password_resets (user_id, token, expires_at, created_at)
      VALUES (?, ?, ?, NOW())
      ON DUPLICATE KEY UPDATE
      token = VALUES(token),
      expires_at = VALUES(expires_at),
      created_at = NOW()
    `;
    
    await executeQuery(query, [user.id, resetToken, resetTokenExpiry]);

    // Send reset email
    try {
      await sendPasswordResetEmail(email, `${user.first_name} ${user.last_name}`, resetToken);
    } catch (emailError) {
      console.error('Failed to send password reset email:', emailError);
      return res.status(500).json(
        formatResponse(false, 'Failed to send password reset email')
      );
    }

    res.json(
      formatResponse(true, 'Password reset email sent')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Reset password
// @route   POST /api/auth/reset-password
// @access  Public
const resetPassword = async (req, res, next) => {
  try {
    const { token, password } = req.body;

    // Find valid reset token
    const query = `
      SELECT pr.*, u.id as user_id, u.email
      FROM password_resets pr
      JOIN users u ON pr.user_id = u.id
      WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = FALSE
    `;
    
    const resets = await executeQuery(query, [token]);
    
    if (resets.length === 0) {
      return res.status(400).json(
        formatResponse(false, 'Invalid or expired reset token')
      );
    }

    const reset = resets[0];

    // Update password
    await User.updatePassword(reset.user_id, password);

    // Mark token as used
    const updateQuery = 'UPDATE password_resets SET used = TRUE WHERE token = ?';
    await executeQuery(updateQuery, [token]);

    res.json(
      formatResponse(true, 'Password reset successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res, next) => {
  try {
    const { current_password, new_password } = req.body;

    // Verify current password
    const user = await User.findById(req.user.id);
    if (!(await User.verifyPassword(user.email, current_password))) {
      return res.status(400).json(
        formatResponse(false, 'Current password is incorrect')
      );
    }

    // Update password
    await User.updatePassword(req.user.id, new_password);

    res.json(
      formatResponse(true, 'Password changed successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Logout user
// @route   POST /api/auth/logout
// @access  Private
const logout = async (req, res, next) => {
  try {
    // In a more complex setup, you might want to blacklist the token
    // For now, we'll just send a success response
    res.json(
      formatResponse(true, 'Logged out successfully')
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  getMe,
  refreshToken,
  forgotPassword,
  resetPassword,
  changePassword,
  logout
};

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Bed, Users, AlertCircle, CheckCircle, Clock, User, Calendar, Activity, Plus } from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const wards = [
  {
    id: "W001",
    name: "General Ward A",
    floor: "2nd Floor",
    totalBeds: 20,
    occupiedBeds: 15,
    availableBeds: 5,
    department: "General Medicine",
    nurseInCharge: "Nurse <PERSON>",
    phone: "******-567-8920",
  },
  {
    id: "W002",
    name: "<PERSON><PERSON>",
    floor: "3rd Floor",
    totalBeds: 12,
    occupiedBeds: 10,
    availableBeds: 2,
    department: "Critical Care",
    nurseInCharge: "Nurse <PERSON>",
    phone: "******-567-8921",
  },
  {
    id: "W003",
    name: "Pediatric Ward",
    floor: "1st Floor",
    totalBeds: 15,
    occupiedBeds: 8,
    availableBeds: 7,
    department: "Pediatrics",
    nurseInCharge: "Nurse Jennifer Davis",
    phone: "******-567-8922",
  },
  {
    id: "W004",
    name: "Maternity Ward",
    floor: "2nd Floor",
    totalBeds: 18,
    occupiedBeds: 12,
    availableBeds: 6,
    department: "Obstetrics",
    nurseInCharge: "Nurse Lisa Anderson",
    phone: "******-567-8923",
  },
]

const beds = [
  {
    id: "B001",
    wardId: "W001",
    wardName: "General Ward A",
    bedNumber: "A-01",
    status: "Occupied",
    patientName: "John Smith",
    patientId: "P001",
    admissionDate: "2024-01-20",
    condition: "Stable",
    doctor: "Dr. Sarah Williams",
    department: "Cardiology",
    expectedDischarge: "2024-01-28",
    notes: "Post-surgery recovery, vitals stable",
  },
  {
    id: "B002",
    wardId: "W001",
    wardName: "General Ward A",
    bedNumber: "A-02",
    status: "Available",
    patientName: null,
    patientId: null,
    admissionDate: null,
    condition: null,
    doctor: null,
    department: null,
    expectedDischarge: null,
    notes: "Ready for new patient",
  },
  {
    id: "B003",
    wardId: "W002",
    wardName: "ICU",
    bedNumber: "ICU-01",
    status: "Occupied",
    patientName: "Robert Wilson",
    patientId: "P005",
    admissionDate: "2024-01-22",
    condition: "Critical",
    doctor: "Dr. Michael Chen",
    department: "Neurology",
    expectedDischarge: "TBD",
    notes: "Requires continuous monitoring",
  },
  {
    id: "B004",
    wardId: "W002",
    wardName: "ICU",
    bedNumber: "ICU-02",
    status: "Maintenance",
    patientName: null,
    patientId: null,
    admissionDate: null,
    condition: null,
    doctor: null,
    department: null,
    expectedDischarge: null,
    notes: "Equipment maintenance scheduled",
  },
  {
    id: "B005",
    wardId: "W003",
    wardName: "Pediatric Ward",
    bedNumber: "P-01",
    status: "Occupied",
    patientName: "Emma Thompson",
    patientId: "P006",
    admissionDate: "2024-01-24",
    condition: "Stable",
    doctor: "Dr. Emily Rodriguez",
    department: "Pediatrics",
    expectedDischarge: "2024-01-26",
    notes: "Recovering from appendectomy",
  },
]

const statusColors = {
  Occupied: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  Available: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Maintenance: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  Reserved: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
}

const conditionColors = {
  Stable: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Critical: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  Moderate: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
}

export default function WardsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [wardFilter, setWardFilter] = useState("All")
  const [statusFilter, setStatusFilter] = useState("All")
  const [selectedBed, setSelectedBed] = useState<(typeof beds)[0] | null>(null)

  const filteredBeds = beds.filter((bed) => {
    const matchesSearch =
      bed.bedNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (bed.patientName && bed.patientName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (bed.patientId && bed.patientId.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesWard = wardFilter === "All" || bed.wardId === wardFilter
    const matchesStatus = statusFilter === "All" || bed.status === statusFilter

    return matchesSearch && matchesWard && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Ward & Bed Management</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Monitor bed occupancy, patient assignments, and ward status
              </p>
            </div>
            <div className="flex gap-3 mt-4 sm:mt-0">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700">
                <Activity className="h-4 w-4 mr-2" />
                Real-time View
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Assign Bed
              </Button>
            </div>
          </div>

          {/* Ward Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {wards.map((ward, index) => (
              <motion.div
                key={ward.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg text-gray-900 dark:text-white">{ward.name}</CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {ward.floor}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{ward.department}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Bed Status */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Bed className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">Beds</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {ward.occupiedBeds}/{ward.totalBeds}
                        </span>
                      </div>

                      {/* Occupancy Bar */}
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            (ward.occupiedBeds / ward.totalBeds) > 0.8
                              ? "bg-red-500"
                              : ward.occupiedBeds / ward.totalBeds > 0.6
                                ? "bg-yellow-500"
                                : "bg-green-500"
                          }`}
                          style={{ width: `${(ward.occupiedBeds / ward.totalBeds) * 100}%` }}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div className="text-center p-2 bg-red-50 dark:bg-red-950 rounded">
                          <div className="font-semibold text-red-600 dark:text-red-400">{ward.occupiedBeds}</div>
                          <div className="text-red-500 dark:text-red-400">Occupied</div>
                        </div>
                        <div className="text-center p-2 bg-green-50 dark:bg-green-950 rounded">
                          <div className="font-semibold text-green-600 dark:text-green-400">{ward.availableBeds}</div>
                          <div className="text-green-500 dark:text-green-400">Available</div>
                        </div>
                      </div>

                      {/* Nurse Info */}
                      <div className="pt-3 border-t border-gray-100 dark:border-gray-700">
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <User className="h-4 w-4 mr-2" />
                          {ward.nurseInCharge}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-500 mt-1">{ward.phone}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by bed number, patient name, or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 dark:border-gray-700"
                  />
                </div>
                <Select value={wardFilter} onValueChange={setWardFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by ward" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Wards</SelectItem>
                    {wards.map((ward) => (
                      <SelectItem key={ward.id} value={ward.id}>
                        {ward.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Status</SelectItem>
                    <SelectItem value="Occupied">Occupied</SelectItem>
                    <SelectItem value="Available">Available</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                    <SelectItem value="Reserved">Reserved</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Beds Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredBeds.map((bed, index) => (
              <motion.div
                key={bed.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card
                  className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800 cursor-pointer"
                  onClick={() => setSelectedBed(bed)}
                >
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Bed Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <Bed className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 dark:text-white">{bed.bedNumber}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{bed.wardName}</p>
                          </div>
                        </div>
                        <Badge className={statusColors[bed.status as keyof typeof statusColors]}>{bed.status}</Badge>
                      </div>

                      {/* Patient Info */}
                      {bed.status === "Occupied" && bed.patientName ? (
                        <div className="space-y-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{bed.patientName}</p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">{bed.patientId}</p>
                          </div>

                          <div className="grid grid-cols-2 gap-3 text-sm">
                            <div>
                              <span className="text-gray-600 dark:text-gray-400">Doctor:</span>
                              <p className="font-medium text-gray-900 dark:text-white">{bed.doctor}</p>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-400">Condition:</span>
                              <Badge
                                className={`${conditionColors[bed.condition as keyof typeof conditionColors]} mt-1`}
                              >
                                {bed.condition}
                              </Badge>
                            </div>
                          </div>

                          <div className="text-sm">
                            <div className="flex items-center text-gray-600 dark:text-gray-400 mb-1">
                              <Calendar className="h-4 w-4 mr-1" />
                              Admitted: {bed.admissionDate}
                            </div>
                            {bed.expectedDischarge && (
                              <div className="flex items-center text-gray-600 dark:text-gray-400">
                                <Clock className="h-4 w-4 mr-1" />
                                Expected discharge: {bed.expectedDischarge}
                              </div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg text-center">
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {bed.status === "Available" ? "Ready for new patient" : bed.notes}
                          </p>
                        </div>
                      )}

                      {/* Status Indicators */}
                      <div className="flex items-center justify-between pt-3 border-t border-gray-100 dark:border-gray-700">
                        <div className="flex items-center space-x-2">
                          {bed.status === "Occupied" && bed.condition === "Critical" && (
                            <AlertCircle className="h-4 w-4 text-red-500" />
                          )}
                          {bed.status === "Available" && <CheckCircle className="h-4 w-4 text-green-500" />}
                          {bed.status === "Maintenance" && <Clock className="h-4 w-4 text-yellow-500" />}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-500">{bed.department}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </main>
      </div>

      {/* Bed Detail Modal */}
      {selectedBed && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Bed {selectedBed.bedNumber}</h2>
                  <p className="text-gray-600 dark:text-gray-400">{selectedBed.wardName}</p>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setSelectedBed(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <Badge className={`${statusColors[selectedBed.status as keyof typeof statusColors]} text-lg px-4 py-2`}>
                  {selectedBed.status}
                </Badge>
                {selectedBed.condition && (
                  <Badge
                    className={`${conditionColors[selectedBed.condition as keyof typeof conditionColors]} text-lg px-4 py-2`}
                  >
                    {selectedBed.condition}
                  </Badge>
                )}
              </div>

              {selectedBed.status === "Occupied" && selectedBed.patientName ? (
                <div className="space-y-4">
                  <Card className="border-gray-200 dark:border-gray-700">
                    <CardHeader>
                      <CardTitle className="text-lg">Patient Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Name:</span>
                        <span className="ml-2 font-medium">{selectedBed.patientName}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Patient ID:</span>
                        <span className="ml-2 font-medium">{selectedBed.patientId}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Attending Doctor:</span>
                        <span className="ml-2 font-medium">{selectedBed.doctor}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Department:</span>
                        <span className="ml-2 font-medium">{selectedBed.department}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Admission Date:</span>
                        <span className="ml-2 font-medium">{selectedBed.admissionDate}</span>
                      </div>
                      {selectedBed.expectedDischarge && (
                        <div>
                          <span className="text-gray-600 dark:text-gray-400">Expected Discharge:</span>
                          <span className="ml-2 font-medium">{selectedBed.expectedDischarge}</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {selectedBed.notes && (
                    <Card className="border-gray-200 dark:border-gray-700">
                      <CardHeader>
                        <CardTitle className="text-lg">Notes</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700 dark:text-gray-300">{selectedBed.notes}</p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardContent className="p-6 text-center">
                    <Bed className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                      {selectedBed.status} Bed
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">{selectedBed.notes}</p>
                  </CardContent>
                </Card>
              )}

              <div className="flex gap-4 pt-4">
                {selectedBed.status === "Available" && (
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Users className="h-4 w-4 mr-2" />
                    Assign Patient
                  </Button>
                )}
                {selectedBed.status === "Occupied" && (
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Discharge Patient
                  </Button>
                )}
                <Button variant="outline">
                  <Activity className="h-4 w-4 mr-2" />
                  View History
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const seedDatabase = async () => {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'hms_database'
    });

    console.log('Connected to database for seeding...');

    // Hash password for demo users
    const hashedPassword = await bcrypt.hash('password123', 12);

    // Get role IDs
    const [roles] = await connection.execute('SELECT id, name FROM roles');
    const roleMap = {};
    roles.forEach(role => {
      roleMap[role.name] = role.id;
    });

    // Get department IDs
    const [departments] = await connection.execute('SELECT id, name FROM departments');
    const deptMap = {};
    departments.forEach(dept => {
      deptMap[dept.name] = dept.id;
    });

    // Seed admin user
    const adminData = [
      '<EMAIL>',
      hashedPassword,
      'System',
      'Administrator',
      '******-567-8900',
      roleMap['admin']
    ];

    await connection.execute(`
      INSERT IGNORE INTO users (email, password, first_name, last_name, phone, role_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, adminData);

    console.log('✅ Admin user created');

    // Seed sample doctors
    const doctors = [
      {
        email: '<EMAIL>',
        first_name: 'Sarah',
        last_name: 'Williams',
        phone: '******-567-8901',
        license_number: 'MD001234',
        specialization: 'Cardiologist',
        department: 'Cardiology',
        experience_years: 15,
        education: 'MD, Harvard Medical School',
        consultation_fee: 200.00
      },
      {
        email: '<EMAIL>',
        first_name: 'Michael',
        last_name: 'Chen',
        phone: '******-567-8902',
        license_number: 'MD001235',
        specialization: 'Neurologist',
        department: 'Neurology',
        experience_years: 12,
        education: 'MD, Johns Hopkins University',
        consultation_fee: 250.00
      },
      {
        email: '<EMAIL>',
        first_name: 'Emily',
        last_name: 'Rodriguez',
        phone: '******-567-8903',
        license_number: 'MD001236',
        specialization: 'Pediatrician',
        department: 'Pediatrics',
        experience_years: 10,
        education: 'MD, Stanford University',
        consultation_fee: 180.00
      }
    ];

    for (const doctor of doctors) {
      // Create user account
      const [userResult] = await connection.execute(`
        INSERT IGNORE INTO users (email, password, first_name, last_name, phone, role_id, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        doctor.email,
        hashedPassword,
        doctor.first_name,
        doctor.last_name,
        doctor.phone,
        roleMap['doctor']
      ]);

      if (userResult.insertId) {
        // Create doctor profile
        await connection.execute(`
          INSERT IGNORE INTO doctors (
            user_id, license_number, specialization, department_id, 
            experience_years, education, consultation_fee, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
          userResult.insertId,
          doctor.license_number,
          doctor.specialization,
          deptMap[doctor.department],
          doctor.experience_years,
          doctor.education,
          doctor.consultation_fee
        ]);
      }
    }

    console.log('✅ Sample doctors created');

    // Seed sample patient
    const patientUserData = [
      '<EMAIL>',
      hashedPassword,
      'John',
      'Smith',
      '+1-************',
      roleMap['patient']
    ];

    const [patientUserResult] = await connection.execute(`
      INSERT IGNORE INTO users (email, password, first_name, last_name, phone, role_id, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, patientUserData);

    if (patientUserResult.insertId) {
      const patientId = `P${Date.now().toString().slice(-6)}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
      
      await connection.execute(`
        INSERT IGNORE INTO patients (
          user_id, patient_id, date_of_birth, gender, address,
          emergency_contact_name, emergency_contact_phone, blood_type,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        patientUserResult.insertId,
        patientId,
        '1980-05-15',
        'male',
        '123 Main Street, City, State 12345',
        'Jane Smith',
        '+1-************',
        'O+'
      ]);
    }

    console.log('✅ Sample patient created');

    // Seed sample medications
    const medications = [
      {
        name: 'Amoxicillin',
        generic_name: 'Amoxicillin',
        category: 'Antibiotic',
        strength: '500mg',
        unit_price: 15.50,
        stock_quantity: 100
      },
      {
        name: 'Lisinopril',
        generic_name: 'Lisinopril',
        category: 'ACE Inhibitor',
        strength: '10mg',
        unit_price: 25.00,
        stock_quantity: 75
      },
      {
        name: 'Metformin',
        generic_name: 'Metformin',
        category: 'Antidiabetic',
        strength: '500mg',
        unit_price: 12.00,
        stock_quantity: 150
      }
    ];

    for (const med of medications) {
      await connection.execute(`
        INSERT IGNORE INTO medications (
          name, generic_name, category, strength, unit_price, stock_quantity,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        med.name,
        med.generic_name,
        med.category,
        med.strength,
        med.unit_price,
        med.stock_quantity
      ]);
    }

    console.log('✅ Sample medications created');

    console.log(`
🎉 Database seeding completed successfully!

Demo Accounts Created:
📧 Admin: <EMAIL>
📧 Doctor: <EMAIL>
📧 Patient: <EMAIL>
🔑 Password for all accounts: password123

🏥 Sample data includes:
- 3 Doctors with different specializations
- 1 Patient record
- 3 Medications in pharmacy
- All departments and roles
    `);

  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// Run seeding
seedDatabase().then(() => {
  process.exit(0);
});

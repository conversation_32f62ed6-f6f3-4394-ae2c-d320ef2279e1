{"name": "hms-backend", "version": "1.0.0", "description": "Hospital Management System Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "migrate": "node scripts/migrate.js", "seed": "node scripts/seed.js"}, "keywords": ["hospital", "management", "system", "healthcare", "api", "nodejs", "express", "mysql"], "author": "HMS Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "multer": "^1.4.5-lts.1", "joi": "^17.11.0", "nodemailer": "^6.9.7", "dotenv": "^16.3.1", "morgan": "^1.10.0", "compression": "^1.7.4", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}
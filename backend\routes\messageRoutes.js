const express = require('express');
const { protect } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', (req, res) => {
  res.json({ success: true, message: 'Get user messages - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get message - Coming soon' });
});

router.post('/', (req, res) => {
  res.json({ success: true, message: 'Send message - Coming soon' });
});

router.put('/:id', (req, res) => {
  res.json({ success: true, message: 'Mark as read - Coming soon' });
});

router.delete('/:id', (req, res) => {
  res.json({ success: true, message: 'Delete message - Coming soon' });
});

module.exports = router;

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/patient/:patientId', (req, res) => {
  res.json({ success: true, message: 'Get patient medical records - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get medical record - Coming soon' });
});

router.post('/', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Create medical record - Coming soon' });
});

router.put('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Update medical record - Coming soon' });
});

router.delete('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Delete medical record - Coming soon' });
});

module.exports = router;

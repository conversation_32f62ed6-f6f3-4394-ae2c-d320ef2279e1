const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin', 'doctor', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Get all prescriptions - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get prescription - Coming soon' });
});

router.post('/', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Create prescription - Coming soon' });
});

router.put('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Update prescription - Coming soon' });
});

router.delete('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Delete prescription - Coming soon' });
});

router.get('/patient/:patientId', (req, res) => {
  res.json({ success: true, message: 'Get patient prescriptions - Coming soon' });
});

router.get('/doctor/:doctorId', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get doctor prescriptions - Coming soon' });
});

module.exports = router;

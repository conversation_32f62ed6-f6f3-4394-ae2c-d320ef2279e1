"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Plus, Edit, Trash2, Settings } from "lucide-react"
import { motion } from "framer-motion"
import { Doctor<PERSON><PERSON>bar } from "@/components/doctor-sidebar"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/doctor-header"

const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

const scheduleData = {
  Monday: [
    { time: "09:00 - 12:00", type: "Consultations", slots: 6, booked: 4 },
    { time: "14:00 - 17:00", type: "Follow-ups", slots: 6, booked: 5 },
  ],
  Tuesday: [
    { time: "09:00 - 12:00", type: "Consultations", slots: 6, booked: 6 },
    { time: "14:00 - 16:00", type: "Surgery", slots: 2, booked: 2 },
  ],
  Wednesday: [
    { time: "09:00 - 12:00", type: "Consultations", slots: 6, booked: 3 },
    { time: "14:00 - 17:00", type: "Follow-ups", slots: 6, booked: 4 },
  ],
  Thursday: [
    { time: "09:00 - 12:00", type: "Consultations", slots: 6, booked: 5 },
    { time: "14:00 - 17:00", type: "Follow-ups", slots: 6, booked: 6 },
  ],
  Friday: [
    { time: "09:00 - 12:00", type: "Consultations", slots: 6, booked: 4 },
    { time: "14:00 - 16:00", type: "Emergency Coverage", slots: 4, booked: 2 },
  ],
  Saturday: [{ time: "09:00 - 13:00", type: "Weekend Clinic", slots: 8, booked: 6 }],
  Sunday: [{ time: "Off", type: "Rest Day", slots: 0, booked: 0 }],
}

const upcomingBreaks = [
  { date: "Jan 28-30", type: "Conference", title: "Medical Conference 2024" },
  { date: "Feb 15-16", type: "Vacation", title: "Personal Leave" },
  { date: "Mar 10", type: "Training", title: "CPR Certification Renewal" },
]

export default function DoctorSchedulePage() {
  const [selectedDay, setSelectedDay] = useState("Monday")

  const getTotalStats = () => {
    let totalSlots = 0
    let totalBooked = 0

    Object.values(scheduleData).forEach((daySchedule) => {
      daySchedule.forEach((session) => {
        totalSlots += session.slots
        totalBooked += session.booked
      })
    })

    return { totalSlots, totalBooked, available: totalSlots - totalBooked }
  }

  const stats = getTotalStats()

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">My Schedule</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage your weekly schedule and availability</p>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700 bg-transparent">
                <Settings className="h-4 w-4 mr-2" />
                Schedule Settings
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add Time Slot
              </Button>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Slots</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{stats.totalSlots}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">This week</p>
                  </div>
                  <Calendar className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Booked</p>
                    <p className="text-3xl font-bold text-green-600 dark:text-green-400">{stats.totalBooked}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {Math.round((stats.totalBooked / stats.totalSlots) * 100)}% utilization
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Available</p>
                    <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{stats.available}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Open slots</p>
                  </div>
                  <Plus className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Working Days</p>
                    <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">6</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Per week</p>
                  </div>
                  <Calendar className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Weekly Schedule */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Weekly Schedule</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your appointment slots for each day of the week
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {weekDays.map((day, index) => (
                      <motion.div
                        key={day}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="border border-gray-100 dark:border-gray-700 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-semibold text-gray-900 dark:text-white text-lg">{day}</h3>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="outline">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="outline">
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-3">
                          {scheduleData[day].map((session, sessionIndex) => (
                            <div
                              key={sessionIndex}
                              className={`flex items-center justify-between p-3 rounded-lg ${
                                session.type === "Rest Day"
                                  ? "bg-gray-100 dark:bg-gray-700"
                                  : "bg-blue-50 dark:bg-blue-950/20 border border-blue-100 dark:border-blue-800"
                              }`}
                            >
                              <div className="flex items-center space-x-4">
                                <div
                                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                                    session.type === "Rest Day"
                                      ? "bg-gray-200 dark:bg-gray-600"
                                      : "bg-blue-100 dark:bg-blue-900"
                                  }`}
                                >
                                  <Clock
                                    className={`h-4 w-4 ${
                                      session.type === "Rest Day"
                                        ? "text-gray-500 dark:text-gray-400"
                                        : "text-blue-600 dark:text-blue-400"
                                    }`}
                                  />
                                </div>
                                <div>
                                  <p className="font-medium text-gray-900 dark:text-white">{session.time}</p>
                                  <p className="text-sm text-gray-500 dark:text-gray-400">{session.type}</p>
                                </div>
                              </div>

                              {session.type !== "Rest Day" && (
                                <div className="flex items-center space-x-4">
                                  <div className="text-right">
                                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                                      {session.booked}/{session.slots} slots
                                    </p>
                                    <div className="flex items-center space-x-2 mt-1">
                                      <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                        <div
                                          className={`h-full transition-all duration-300 ${
                                            session.booked === session.slots
                                              ? "bg-red-500"
                                              : session.booked / session.slots > 0.8
                                                ? "bg-orange-500"
                                                : "bg-green-500"
                                          }`}
                                          style={{ width: `${(session.booked / session.slots) * 100}%` }}
                                        />
                                      </div>
                                      <Badge
                                        variant="secondary"
                                        className={
                                          session.booked === session.slots
                                            ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                            : session.booked / session.slots > 0.8
                                              ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                              : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                        }
                                      >
                                        {session.booked === session.slots ? "Full" : "Available"}
                                      </Badge>
                                    </div>
                                  </div>
                                  <Button size="sm" variant="ghost">
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Upcoming Breaks */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Upcoming Breaks</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Scheduled time off and events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingBreaks.map((breakItem, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-700"
                      >
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white text-sm">{breakItem.title}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{breakItem.date}</p>
                        </div>
                        <Badge
                          variant="secondary"
                          className={
                            breakItem.type === "Vacation"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              : breakItem.type === "Conference"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                : "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                          }
                        >
                          {breakItem.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                  <Button variant="outline" className="w-full mt-4 bg-transparent">
                    <Plus className="h-4 w-4 mr-2" />
                    Request Time Off
                  </Button>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <Calendar className="h-4 w-4 mr-2" />
                      Block Time Slot
                    </Button>
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <Clock className="h-4 w-4 mr-2" />
                      Set Recurring Schedule
                    </Button>
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <Settings className="h-4 w-4 mr-2" />
                      Schedule Preferences
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

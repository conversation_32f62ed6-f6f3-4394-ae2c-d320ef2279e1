"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, FileText, Activity, Heart, Plus, Eye } from "lucide-react"
import { motion } from "framer-motion"
import { PatientSidebar } from "@/components/patient-sidebar"
import { PatientHeader } from "@/components/patient-header"

const stats = [
  {
    title: "Upcoming Appointments",
    value: "2",
    change: "Next: Tomorrow 10:00 AM",
    trend: "up",
    icon: Calendar,
    color: "text-green-600 dark:text-green-400",
  },
  {
    title: "Active Prescriptions",
    value: "3",
    change: "1 needs refill",
    trend: "up",
    icon: FileText,
    color: "text-blue-600 dark:text-blue-400",
  },
  {
    title: "Health Score",
    value: "85%",
    change: "+5% this month",
    trend: "up",
    icon: Heart,
    color: "text-red-600 dark:text-red-400",
  },
  {
    title: "Last Visit",
    value: "3 days",
    change: "<PERSON><PERSON> <PERSON>",
    trend: "neutral",
    icon: Clock,
    color: "text-purple-600 dark:text-purple-400",
  },
]

const upcomingAppointments = [
  {
    date: "Tomorrow",
    time: "10:00 AM",
    doctor: "Dr. <PERSON> Williams",
    specialty: "Cardiologist",
    type: "Follow-up",
    status: "confirmed",
  },
  {
    date: "Jan 25",
    time: "02:30 PM",
    doctor: "Dr. Michael Johnson",
    specialty: "General Physician",
    type: "Check-up",
    status: "confirmed",
  },
]

const activePrescriptions = [
  {
    medication: "Lisinopril 10mg",
    dosage: "Once daily",
    prescribedBy: "Dr. Sarah Williams",
    refillDate: "Jan 30, 2024",
    status: "active",
  },
  {
    medication: "Metformin 500mg",
    dosage: "Twice daily",
    prescribedBy: "Dr. Michael Johnson",
    refillDate: "Feb 15, 2024",
    status: "active",
  },
  {
    medication: "Aspirin 81mg",
    dosage: "Once daily",
    prescribedBy: "Dr. Sarah Williams",
    refillDate: "Jan 22, 2024",
    status: "needs_refill",
  },
]

const healthMetrics = [
  { metric: "Blood Pressure", value: "120/80", status: "normal", lastUpdated: "2 days ago" },
  { metric: "Heart Rate", value: "72 bpm", status: "normal", lastUpdated: "2 days ago" },
  { metric: "Weight", value: "165 lbs", status: "stable", lastUpdated: "1 week ago" },
  { metric: "Blood Sugar", value: "95 mg/dL", status: "normal", lastUpdated: "3 days ago" },
]

const recentActivity = [
  { activity: "Lab results received", time: "2 hours ago", type: "lab" },
  { activity: "Appointment confirmed with Dr. Williams", time: "1 day ago", type: "appointment" },
  { activity: "Prescription refilled - Lisinopril", time: "3 days ago", type: "prescription" },
  { activity: "Health metrics updated", time: "2 days ago", type: "health" },
]

export default function PatientDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <PatientSidebar />

      <div className="lg:pl-64">
        <PatientHeader />

        <main className="p-6">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Welcome back, John</h1>
            <p className="text-gray-600 dark:text-gray-400">
              You have 2 upcoming appointments and 1 prescription that needs refill.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{stat.change}</p>
                      </div>
                      <div className={`p-3 rounded-2xl bg-gray-50 dark:bg-gray-700 ${stat.color}`}>
                        <stat.icon className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Upcoming Appointments */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-gray-900 dark:text-white">Upcoming Appointments</CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-400">
                      Your scheduled appointments
                    </CardDescription>
                  </div>
                  <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Book Appointment
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {upcomingAppointments.map((appointment, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                            <Calendar className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{appointment.doctor}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {appointment.date} at {appointment.time} • {appointment.specialty}
                            </p>
                            <p className="text-xs text-gray-400 dark:text-gray-500">{appointment.type}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            {appointment.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Active Prescriptions */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Active Prescriptions</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Your current medications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activePrescriptions.map((prescription, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700"
                      >
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{prescription.medication}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{prescription.dosage}</p>
                          <p className="text-xs text-gray-400 dark:text-gray-500">
                            Prescribed by {prescription.prescribedBy}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge
                            variant={prescription.status === "needs_refill" ? "destructive" : "default"}
                            className={
                              prescription.status === "needs_refill"
                                ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                            }
                          >
                            {prescription.status === "needs_refill" ? "Needs Refill" : "Active"}
                          </Badge>
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            Refill: {prescription.refillDate}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Health Metrics */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <Activity className="h-5 w-5 mr-2 text-green-500" />
                    Health Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {healthMetrics.map((metric, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{metric.metric}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{metric.lastUpdated}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-bold text-gray-900 dark:text-white">{metric.value}</p>
                          <Badge
                            variant="secondary"
                            className={
                              metric.status === "normal"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            }
                          >
                            {metric.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Activity */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentActivity.map((activity, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700"
                      >
                        <div
                          className={`w-2 h-2 rounded-full mt-2 ${
                            activity.type === "lab"
                              ? "bg-purple-500"
                              : activity.type === "appointment"
                                ? "bg-green-500"
                                : activity.type === "prescription"
                                  ? "bg-blue-500"
                                  : "bg-orange-500"
                          }`}
                        />
                        <div className="flex-1">
                          <p className="text-sm text-gray-900 dark:text-white">{activity.activity}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{activity.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Eye, Edit, Pill, Calendar, Clock, CheckCircle, Printer } from "lucide-react"
import { motion } from "framer-motion"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { DoctorHeader } from "@/components/doctor-header"

const prescriptions = [
  {
    id: "RX001",
    patientId: "P001",
    patientName: "<PERSON>",
    patientAge: 45,
    prescriptionDate: "2024-01-20",
    medications: [
      {
        name: "Lisinopril",
        dosage: "10mg",
        frequency: "Once daily",
        duration: "30 days",
        instructions: "Take with or without food",
        quantity: "30 tablets",
      },
      {
        name: "Hydrochlorothiazide",
        dosage: "25mg",
        frequency: "Once daily",
        duration: "30 days",
        instructions: "Take in the morning",
        quantity: "30 tablets",
      },
    ],
    diagnosis: "Hypertension Stage 1",
    status: "active",
    refills: 2,
    pharmacy: "HealthCare Pharmacy",
    notes: "Monitor blood pressure weekly. Return if side effects occur.",
  },
  {
    id: "RX002",
    patientId: "P002",
    patientName: "Sarah Johnson",
    patientAge: 32,
    prescriptionDate: "2024-01-19",
    medications: [
      {
        name: "Metformin",
        dosage: "500mg",
        frequency: "Twice daily",
        duration: "90 days",
        instructions: "Take with meals",
        quantity: "180 tablets",
      },
    ],
    diagnosis: "Type 2 Diabetes Mellitus",
    status: "active",
    refills: 5,
    pharmacy: "City Pharmacy",
    notes: "Check blood glucose levels regularly. Follow up in 3 months.",
  },
  {
    id: "RX003",
    patientId: "P003",
    patientName: "Michael Brown",
    patientAge: 58,
    prescriptionDate: "2024-01-18",
    medications: [
      {
        name: "Atorvastatin",
        dosage: "20mg",
        frequency: "Once daily",
        duration: "90 days",
        instructions: "Take at bedtime",
        quantity: "90 tablets",
      },
      {
        name: "Aspirin",
        dosage: "81mg",
        frequency: "Once daily",
        duration: "90 days",
        instructions: "Take with food",
        quantity: "90 tablets",
      },
    ],
    diagnosis: "Hyperlipidemia",
    status: "active",
    refills: 3,
    pharmacy: "MedPlus Pharmacy",
    notes: "Monitor liver function tests in 6 weeks.",
  },
  {
    id: "RX004",
    patientId: "P004",
    patientName: "Emily Davis",
    patientAge: 28,
    prescriptionDate: "2024-01-17",
    medications: [
      {
        name: "Sumatriptan",
        dosage: "50mg",
        frequency: "As needed",
        duration: "30 days",
        instructions: "Take at onset of migraine",
        quantity: "9 tablets",
      },
    ],
    diagnosis: "Migraine",
    status: "completed",
    refills: 1,
    pharmacy: "Wellness Pharmacy",
    notes: "Maximum 2 doses per 24 hours. Avoid triggers.",
  },
]

const activePrescriptions = prescriptions.filter((rx) => rx.status === "active")
const recentPrescriptions = prescriptions.slice(0, 3)

export default function DoctorPrescriptionsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPrescription, setSelectedPrescription] = useState(null)

  const filteredPrescriptions = prescriptions.filter(
    (prescription) =>
      prescription.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prescription.diagnosis.toLowerCase().includes(searchTerm.toLowerCase()) ||
      prescription.medications.some((med) => med.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      prescription.id.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Prescriptions</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage patient prescriptions and medication orders</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              New Prescription
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Prescriptions</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{prescriptions.length}</p>
                  </div>
                  <Pill className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active</p>
                    <p className="text-3xl font-bold text-green-600 dark:text-green-400">
                      {activePrescriptions.length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">This Week</p>
                    <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">{prescriptions.length}</p>
                  </div>
                  <Calendar className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Refills Due</p>
                    <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">2</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs defaultValue="all" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-96">
              <TabsTrigger value="all">All Prescriptions ({prescriptions.length})</TabsTrigger>
              <TabsTrigger value="active">Active ({activePrescriptions.length})</TabsTrigger>
              <TabsTrigger value="recent">Recent (3)</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {/* Search */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search prescriptions by patient name, medication, or diagnosis..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Prescriptions Table */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">All Prescriptions</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Complete list of patient prescriptions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Patient</TableHead>
                        <TableHead>Medications</TableHead>
                        <TableHead>Diagnosis</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Refills</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPrescriptions.map((prescription) => (
                        <TableRow key={prescription.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src="/placeholder.svg?height=40&width=40" alt={prescription.patientName} />
                                <AvatarFallback>
                                  {prescription.patientName
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">{prescription.patientName}</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {prescription.patientAge} years • {prescription.patientId}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {prescription.medications.slice(0, 2).map((med, index) => (
                                <div key={index} className="text-sm">
                                  <span className="font-medium text-gray-900 dark:text-white">{med.name}</span>
                                  <span className="text-gray-500 dark:text-gray-400 ml-2">
                                    {med.dosage} - {med.frequency}
                                  </span>
                                </div>
                              ))}
                              {prescription.medications.length > 2 && (
                                <p className="text-xs text-gray-400">+{prescription.medications.length - 2} more</p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium text-gray-900 dark:text-white">{prescription.diagnosis}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{prescription.prescriptionDate}</span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                prescription.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                              }
                            >
                              {prescription.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{prescription.refills} remaining</span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl">
                                  <DialogHeader>
                                    <DialogTitle>Prescription Details - {prescription.patientName}</DialogTitle>
                                    <DialogDescription>
                                      Complete prescription information and medication details
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="py-4">
                                    <div className="grid grid-cols-2 gap-6 mb-6">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Patient Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Name:</span> {prescription.patientName}
                                          </p>
                                          <p>
                                            <span className="font-medium">Age:</span> {prescription.patientAge} years
                                          </p>
                                          <p>
                                            <span className="font-medium">Patient ID:</span> {prescription.patientId}
                                          </p>
                                          <p>
                                            <span className="font-medium">Prescription ID:</span> {prescription.id}
                                          </p>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Prescription Details
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Date:</span> {prescription.prescriptionDate}
                                          </p>
                                          <p>
                                            <span className="font-medium">Diagnosis:</span> {prescription.diagnosis}
                                          </p>
                                          <p>
                                            <span className="font-medium">Status:</span> {prescription.status}
                                          </p>
                                          <p>
                                            <span className="font-medium">Pharmacy:</span> {prescription.pharmacy}
                                          </p>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="mb-6">
                                      <h4 className="font-medium text-gray-900 dark:text-white mb-4">Medications</h4>
                                      <Table>
                                        <TableHeader>
                                          <TableRow>
                                            <TableHead>Medication</TableHead>
                                            <TableHead>Dosage</TableHead>
                                            <TableHead>Frequency</TableHead>
                                            <TableHead>Duration</TableHead>
                                            <TableHead>Quantity</TableHead>
                                            <TableHead>Instructions</TableHead>
                                          </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                          {prescription.medications.map((medication, index) => (
                                            <TableRow key={index}>
                                              <TableCell className="font-medium">{medication.name}</TableCell>
                                              <TableCell>{medication.dosage}</TableCell>
                                              <TableCell>{medication.frequency}</TableCell>
                                              <TableCell>{medication.duration}</TableCell>
                                              <TableCell>{medication.quantity}</TableCell>
                                              <TableCell>{medication.instructions}</TableCell>
                                            </TableRow>
                                          ))}
                                        </TableBody>
                                      </Table>
                                    </div>

                                    <div>
                                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Clinical Notes</h4>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">{prescription.notes}</p>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                              <Button size="sm" variant="outline">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Printer className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="active" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Active Prescriptions</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Currently active patient prescriptions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activePrescriptions.map((prescription, index) => (
                      <motion.div
                        key={prescription.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="p-6 rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                              <Pill className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">
                                  {prescription.patientName}
                                </h3>
                                <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  {prescription.status}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{prescription.diagnosis}</p>
                              <div className="space-y-1">
                                {prescription.medications.map((med, medIndex) => (
                                  <div key={medIndex} className="text-sm">
                                    <span className="font-medium text-gray-900 dark:text-white">{med.name}</span>
                                    <span className="text-gray-500 dark:text-gray-400 ml-2">
                                      {med.dosage} - {med.frequency}
                                    </span>
                                  </div>
                                ))}
                              </div>
                              <p className="text-xs text-green-600 dark:text-green-400 mt-2">
                                {prescription.refills} refills remaining • {prescription.pharmacy}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recent" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Recent Prescriptions</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Most recently prescribed medications
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentPrescriptions.map((prescription, index) => (
                      <div
                        key={prescription.id}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src="/placeholder.svg?height=48&width=48" alt={prescription.patientName} />
                            <AvatarFallback>
                              {prescription.patientName
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{prescription.patientName}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{prescription.diagnosis}</p>
                            <p className="text-xs text-gray-400 dark:text-gray-500">
                              {prescription.medications.length} medication(s) • {prescription.prescriptionDate}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            className={
                              prescription.status === "active"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                            }
                          >
                            {prescription.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Eye, Edit, Phone, Mail, Calendar, FileText, Activity } from "lucide-react"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/doctor-header"

const patients = [
  {
    id: "P001",
    name: "<PERSON>",
    age: 45,
    gender: "Male",
    phone: "+****************",
    email: "<EMAIL>",
    lastVisit: "2024-01-20",
    nextAppointment: "2024-01-25",
    condition: "Hypertension",
    status: "stable",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "P002",
    name: "Sarah Johnson",
    age: 32,
    gender: "Female",
    phone: "+****************",
    email: "<EMAIL>",
    lastVisit: "2024-01-19",
    nextAppointment: "2024-01-26",
    condition: "Diabetes Type 2",
    status: "monitoring",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "P003",
    name: "Michael Brown",
    age: 58,
    gender: "Male",
    phone: "+****************",
    email: "<EMAIL>",
    lastVisit: "2024-01-18",
    nextAppointment: "2024-01-28",
    condition: "Arthritis",
    status: "improving",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "P004",
    name: "Emily Davis",
    age: 28,
    gender: "Female",
    phone: "+****************",
    email: "<EMAIL>",
    lastVisit: "2024-01-17",
    nextAppointment: "2024-01-30",
    condition: "Migraine",
    status: "stable",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const recentPatients = patients.slice(0, 3)
const criticalPatients = patients.filter((p) => p.status === "monitoring")

export default function DoctorPatientsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedPatient, setSelectedPatient] = useState(null)

  const filteredPatients = patients.filter(
    (patient) =>
      patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.condition.toLowerCase().includes(searchTerm.toLowerCase()) ||
      patient.id.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">My Patients</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage and monitor your patients' health records</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add New Patient
            </Button>
          </div>

          <Tabs defaultValue="all" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-96">
              <TabsTrigger value="all">All Patients ({patients.length})</TabsTrigger>
              <TabsTrigger value="recent">Recent (3)</TabsTrigger>
              <TabsTrigger value="critical">Critical ({criticalPatients.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {/* Search */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search patients by name, condition, or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Patients Table */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">All Patients</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Complete list of your patients
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Patient</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Condition</TableHead>
                        <TableHead>Last Visit</TableHead>
                        <TableHead>Next Appointment</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPatients.map((patient) => (
                        <TableRow key={patient.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={patient.avatar || "/placeholder.svg"} alt={patient.name} />
                                <AvatarFallback>
                                  {patient.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">{patient.name}</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {patient.age} years • {patient.gender} • {patient.id}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <Phone className="h-3 w-3 mr-1" />
                                {patient.phone}
                              </div>
                              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <Mail className="h-3 w-3 mr-1" />
                                {patient.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium text-gray-900 dark:text-white">{patient.condition}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{patient.lastVisit}</span>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{patient.nextAppointment}</span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                patient.status === "stable"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : patient.status === "monitoring"
                                    ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                    : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                              }
                            >
                              {patient.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl">
                                  <DialogHeader>
                                    <DialogTitle>Patient Details - {patient.name}</DialogTitle>
                                    <DialogDescription>
                                      Complete patient information and medical history
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="grid grid-cols-2 gap-6 py-4">
                                    <div className="space-y-4">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Personal Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Age:</span> {patient.age} years
                                          </p>
                                          <p>
                                            <span className="font-medium">Gender:</span> {patient.gender}
                                          </p>
                                          <p>
                                            <span className="font-medium">Phone:</span> {patient.phone}
                                          </p>
                                          <p>
                                            <span className="font-medium">Email:</span> {patient.email}
                                          </p>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Medical Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Primary Condition:</span> {patient.condition}
                                          </p>
                                          <p>
                                            <span className="font-medium">Status:</span> {patient.status}
                                          </p>
                                          <p>
                                            <span className="font-medium">Last Visit:</span> {patient.lastVisit}
                                          </p>
                                          <p>
                                            <span className="font-medium">Next Appointment:</span>{" "}
                                            {patient.nextAppointment}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="space-y-4">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Quick Actions
                                        </h4>
                                        <div className="space-y-2">
                                          <Button size="sm" className="w-full justify-start">
                                            <Calendar className="h-4 w-4 mr-2" />
                                            Schedule Appointment
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="w-full justify-start bg-transparent"
                                          >
                                            <FileText className="h-4 w-4 mr-2" />
                                            View Medical Records
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="w-full justify-start bg-transparent"
                                          >
                                            <Activity className="h-4 w-4 mr-2" />
                                            View Lab Results
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                              <Button size="sm" variant="outline">
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recent" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Recent Patients</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Patients you've seen recently
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {recentPatients.map((patient) => (
                      <div
                        key={patient.id}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={patient.avatar || "/placeholder.svg"} alt={patient.name} />
                            <AvatarFallback>
                              {patient.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{patient.name}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{patient.condition}</p>
                            <p className="text-xs text-gray-400 dark:text-gray-500">Last visit: {patient.lastVisit}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            variant="secondary"
                            className={
                              patient.status === "stable"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : patient.status === "monitoring"
                                  ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                  : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            }
                          >
                            {patient.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="critical" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Critical Patients</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Patients requiring close monitoring
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4">
                    {criticalPatients.map((patient) => (
                      <div
                        key={patient.id}
                        className="flex items-center justify-between p-4 rounded-lg border border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20"
                      >
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={patient.avatar || "/placeholder.svg"} alt={patient.name} />
                            <AvatarFallback>
                              {patient.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{patient.name}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{patient.condition}</p>
                            <p className="text-xs text-orange-600 dark:text-orange-400">Requires monitoring</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge className="bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                            {patient.status}
                          </Badge>
                          <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

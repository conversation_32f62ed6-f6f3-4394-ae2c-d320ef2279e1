const Joi = require('joi');

// Generic validation middleware
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

// Common validation schemas
const schemas = {
  // User registration/login
  register: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    first_name: Joi.string().min(2).max(50).required(),
    last_name: Joi.string().min(2).max(50).required(),
    phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).required(),
    role: Joi.string().valid('doctor', 'patient', 'nurse', 'admin').required()
  }),

  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Patient registration
  patientRegistration: Joi.object({
    first_name: Joi.string().min(2).max(50).required(),
    last_name: Joi.string().min(2).max(50).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).required(),
    date_of_birth: Joi.date().max('now').required(),
    gender: Joi.string().valid('male', 'female', 'other').required(),
    address: Joi.string().max(255).required(),
    emergency_contact_name: Joi.string().max(100).required(),
    emergency_contact_phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).required(),
    blood_type: Joi.string().valid('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-').optional(),
    allergies: Joi.string().max(500).optional(),
    medical_history: Joi.string().max(1000).optional(),
    insurance_provider: Joi.string().max(100).optional(),
    insurance_number: Joi.string().max(50).optional()
  }),

  // Appointment booking
  appointmentBooking: Joi.object({
    patient_id: Joi.number().integer().positive().required(),
    doctor_id: Joi.number().integer().positive().required(),
    appointment_date: Joi.date().min('now').required(),
    appointment_time: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).required(),
    reason: Joi.string().max(500).required(),
    type: Joi.string().valid('consultation', 'follow-up', 'emergency', 'check-up').required(),
    notes: Joi.string().max(1000).optional()
  }),

  // Doctor profile
  doctorProfile: Joi.object({
    specialization: Joi.string().max(100).required(),
    license_number: Joi.string().max(50).required(),
    experience_years: Joi.number().integer().min(0).max(50).required(),
    education: Joi.string().max(500).required(),
    bio: Joi.string().max(1000).optional(),
    consultation_fee: Joi.number().positive().required(),
    languages: Joi.array().items(Joi.string().max(50)).optional(),
    availability: Joi.object().optional()
  }),

  // Prescription
  prescription: Joi.object({
    patient_id: Joi.number().integer().positive().required(),
    doctor_id: Joi.number().integer().positive().required(),
    medications: Joi.array().items(
      Joi.object({
        medication_name: Joi.string().max(100).required(),
        dosage: Joi.string().max(50).required(),
        frequency: Joi.string().max(50).required(),
        duration: Joi.string().max(50).required(),
        instructions: Joi.string().max(500).optional()
      })
    ).min(1).required(),
    notes: Joi.string().max(500).optional()
  }),

  // Medical record
  medicalRecord: Joi.object({
    patient_id: Joi.number().integer().positive().required(),
    doctor_id: Joi.number().integer().positive().required(),
    visit_date: Joi.date().max('now').required(),
    diagnosis: Joi.string().max(500).required(),
    symptoms: Joi.string().max(500).required(),
    treatment: Joi.string().max(1000).required(),
    notes: Joi.string().max(1000).optional(),
    vital_signs: Joi.object({
      blood_pressure: Joi.string().optional(),
      heart_rate: Joi.number().integer().min(30).max(200).optional(),
      temperature: Joi.number().min(90).max(110).optional(),
      weight: Joi.number().positive().optional(),
      height: Joi.number().positive().optional()
    }).optional()
  }),

  // Lab test
  labTest: Joi.object({
    patient_id: Joi.number().integer().positive().required(),
    doctor_id: Joi.number().integer().positive().required(),
    test_name: Joi.string().max(100).required(),
    test_type: Joi.string().max(50).required(),
    instructions: Joi.string().max(500).optional(),
    urgent: Joi.boolean().default(false)
  }),

  // Contact form
  contactForm: Joi.object({
    name: Joi.string().min(2).max(100).required(),
    email: Joi.string().email().required(),
    phone: Joi.string().pattern(/^[+]?[1-9][\d\s\-\(\)]{7,15}$/).optional(),
    department: Joi.string().max(100).required(),
    subject: Joi.string().max(200).required(),
    message: Joi.string().max(1000).required()
  }),

  // Password change
  changePassword: Joi.object({
    current_password: Joi.string().required(),
    new_password: Joi.string().min(6).required(),
    confirm_password: Joi.string().valid(Joi.ref('new_password')).required()
  }),

  // Password reset
  resetPassword: Joi.object({
    token: Joi.string().required(),
    password: Joi.string().min(6).required(),
    confirm_password: Joi.string().valid(Joi.ref('password')).required()
  })
};

module.exports = {
  validate,
  schemas
};

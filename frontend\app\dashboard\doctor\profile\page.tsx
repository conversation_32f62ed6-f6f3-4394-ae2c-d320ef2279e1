"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar, Award, BookOpen, Clock, Edit, Save, Camera, Star, Users, FileText } from "lucide-react"
import { DoctorS<PERSON>bar } from "@/components/doctor-sidebar"
import { DoctorHeader } from "@/components/doctor-header"

const doctorProfile = {
  name: "Dr. <PERSON>",
  title: "Cardiologist",
  email: "<EMAIL>",
  phone: "+****************",
  address: "123 Medical Center Dr, Healthcare City, HC 12345",
  licenseNumber: "MD123456789",
  yearsOfExperience: 12,
  specializations: ["Cardiology", "Internal Medicine", "Preventive Care"],
  languages: ["English", "Spanish", "French"],
  bio: "Dr. <PERSON> Williams is a board-certified cardiologist with over 12 years of experience in treating cardiovascular diseases. She specializes in preventive cardiology and has published numerous research papers in peer-reviewed journals.",
  education: [
    {
      degree: "MD - Doctor of Medicine",
      institution: "Harvard Medical School",
      year: "2012",
    },
    {
      degree: "Residency in Internal Medicine",
      institution: "Johns Hopkins Hospital",
      year: "2015",
    },
    {
      degree: "Fellowship in Cardiology",
      institution: "Mayo Clinic",
      year: "2017",
    },
  ],
  certifications: [
    {
      name: "Board Certified in Cardiology",
      issuer: "American Board of Internal Medicine",
      year: "2017",
      expires: "2027",
    },
    {
      name: "Advanced Cardiac Life Support (ACLS)",
      issuer: "American Heart Association",
      year: "2023",
      expires: "2025",
    },
    {
      name: "Echocardiography Certification",
      issuer: "National Board of Echocardiography",
      year: "2018",
      expires: "2028",
    },
  ],
  achievements: [
    {
      title: "Excellence in Patient Care Award",
      year: "2023",
      description: "Recognized for outstanding patient satisfaction scores",
    },
    {
      title: "Research Publication Award",
      year: "2022",
      description: "Published groundbreaking research on preventive cardiology",
    },
    {
      title: "Medical Innovation Award",
      year: "2021",
      description: "Developed new treatment protocol for heart disease prevention",
    },
  ],
}

const stats = [
  { label: "Total Patients", value: "1,247", icon: Users },
  { label: "Years Experience", value: "12", icon: Calendar },
  { label: "Patient Rating", value: "4.9", icon: Star },
  { label: "Publications", value: "23", icon: FileText },
]

export default function DoctorProfilePage() {
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState(doctorProfile)

  const handleSave = () => {
    // Handle save logic here
    setIsEditing(false)
    console.log("Profile updated:", formData)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">My Profile</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage your professional information and credentials</p>
            </div>
            <div className="flex items-center space-x-3">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={() => setIsEditing(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)} className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              )}
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <Card key={index} className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.label}</p>
                      <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                    </div>
                    <stat.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Profile Information */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="personal" className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="personal">Personal</TabsTrigger>
                  <TabsTrigger value="professional">Professional</TabsTrigger>
                  <TabsTrigger value="education">Education</TabsTrigger>
                  <TabsTrigger value="achievements">Achievements</TabsTrigger>
                </TabsList>

                <TabsContent value="personal" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Personal Information</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Your basic personal and contact information
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name</Label>
                          {isEditing ? (
                            <Input
                              id="name"
                              value={formData.name}
                              onChange={(e) => handleInputChange("name", e.target.value)}
                            />
                          ) : (
                            <p className="text-gray-900 dark:text-white font-medium">{formData.name}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="title">Professional Title</Label>
                          {isEditing ? (
                            <Input
                              id="title"
                              value={formData.title}
                              onChange={(e) => handleInputChange("title", e.target.value)}
                            />
                          ) : (
                            <p className="text-gray-900 dark:text-white font-medium">{formData.title}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address</Label>
                          {isEditing ? (
                            <Input
                              id="email"
                              type="email"
                              value={formData.email}
                              onChange={(e) => handleInputChange("email", e.target.value)}
                            />
                          ) : (
                            <p className="text-gray-900 dark:text-white font-medium">{formData.email}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone Number</Label>
                          {isEditing ? (
                            <Input
                              id="phone"
                              value={formData.phone}
                              onChange={(e) => handleInputChange("phone", e.target.value)}
                            />
                          ) : (
                            <p className="text-gray-900 dark:text-white font-medium">{formData.phone}</p>
                          )}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="address">Address</Label>
                        {isEditing ? (
                          <Input
                            id="address"
                            value={formData.address}
                            onChange={(e) => handleInputChange("address", e.target.value)}
                          />
                        ) : (
                          <p className="text-gray-900 dark:text-white font-medium">{formData.address}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="bio">Professional Bio</Label>
                        {isEditing ? (
                          <Textarea
                            id="bio"
                            rows={4}
                            value={formData.bio}
                            onChange={(e) => handleInputChange("bio", e.target.value)}
                          />
                        ) : (
                          <p className="text-gray-600 dark:text-gray-400">{formData.bio}</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="professional" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Professional Details</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Your medical license and professional information
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label>Medical License Number</Label>
                          <p className="text-gray-900 dark:text-white font-medium">{formData.licenseNumber}</p>
                        </div>
                        <div className="space-y-2">
                          <Label>Years of Experience</Label>
                          <p className="text-gray-900 dark:text-white font-medium">
                            {formData.yearsOfExperience} years
                          </p>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>Specializations</Label>
                        <div className="flex flex-wrap gap-2">
                          {formData.specializations.map((spec, index) => (
                            <Badge
                              key={index}
                              className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                            >
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label>Languages</Label>
                        <div className="flex flex-wrap gap-2">
                          {formData.languages.map((lang, index) => (
                            <Badge key={index} variant="secondary">
                              {lang}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="education" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Education & Certifications</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Your educational background and professional certifications
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Education</h4>
                        <div className="space-y-4">
                          {formData.education.map((edu, index) => (
                            <div
                              key={index}
                              className="flex items-start space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700"
                            >
                              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                              </div>
                              <div className="flex-1">
                                <p className="font-medium text-gray-900 dark:text-white">{edu.degree}</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{edu.institution}</p>
                                <p className="text-xs text-gray-500 dark:text-gray-500">Graduated: {edu.year}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Certifications</h4>
                        <div className="space-y-4">
                          {formData.certifications.map((cert, index) => (
                            <div
                              key={index}
                              className="flex items-start space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700"
                            >
                              <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                <Award className="h-5 w-5 text-green-600 dark:text-green-400" />
                              </div>
                              <div className="flex-1">
                                <p className="font-medium text-gray-900 dark:text-white">{cert.name}</p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">{cert.issuer}</p>
                                <p className="text-xs text-gray-500 dark:text-gray-500">
                                  Issued: {cert.year} • Expires: {cert.expires}
                                </p>
                              </div>
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                Active
                              </Badge>
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="achievements" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Achievements & Awards</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Recognition and achievements in your medical career
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {formData.achievements.map((achievement, index) => (
                          <div
                            key={index}
                            className="flex items-start space-x-4 p-4 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border border-yellow-200 dark:border-yellow-800"
                          >
                            <div className="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                              <Award className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 dark:text-white">{achievement.title}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{achievement.description}</p>
                              <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-2">
                                Awarded in {achievement.year}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Profile Sidebar */}
            <div className="space-y-6">
              {/* Profile Picture */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Profile Picture</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="relative inline-block">
                    <Avatar className="h-32 w-32 mx-auto">
                      <AvatarImage src="/placeholder.svg?height=128&width=128" alt="Dr. Sarah Williams" />
                      <AvatarFallback className="text-2xl">SW</AvatarFallback>
                    </Avatar>
                    {isEditing && (
                      <Button size="sm" className="absolute bottom-0 right-0 rounded-full w-10 h-10 p-0">
                        <Camera className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">{formData.name}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">{formData.title}</p>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Quick Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">Online Status</span>
                      </div>
                      <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Available
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">Next Appointment</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">10:00 AM</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600 dark:text-gray-400">Today's Patients</span>
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">8</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

const Patient = require('../models/Patient');
const User = require('../models/User');
const { executeQuery } = require('../config/database');
const { formatResponse, getPagination, getPaginationMeta } = require('../utils/helpers');

// @desc    Get all patients
// @route   GET /api/patients
// @access  Private (Admin, Doctor, Nurse)
const getPatients = async (req, res, next) => {
  try {
    const { page = 0, size = 10, search, gender, blood_type, age_min, age_max } = req.query;
    const { limit, offset } = getPagination(page, size);

    const filters = {
      search,
      gender,
      blood_type,
      age_min: age_min ? parseInt(age_min) : null,
      age_max: age_max ? parseInt(age_max) : null
    };

    const result = await Patient.findAll(Math.floor(offset / limit), limit, filters);
    const meta = getPaginationMeta(page, limit, result.total);

    res.json(
      formatResponse(true, 'Patients retrieved successfully', result.patients, meta)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get single patient
// @route   GET /api/patients/:id
// @access  Private
const getPatient = async (req, res, next) => {
  try {
    const patient = await Patient.findById(req.params.id);

    if (!patient) {
      return res.status(404).json(
        formatResponse(false, 'Patient not found')
      );
    }

    // Check authorization - patients can only view their own data
    if (req.user.role_name === 'patient') {
      const userPatient = await Patient.findByUserId(req.user.id);
      if (!userPatient || userPatient.id !== parseInt(req.params.id)) {
        return res.status(403).json(
          formatResponse(false, 'Not authorized to access this patient data')
        );
      }
    }

    res.json(
      formatResponse(true, 'Patient retrieved successfully', patient)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Create new patient
// @route   POST /api/patients
// @access  Private (Admin, Nurse)
const createPatient = async (req, res, next) => {
  try {
    const {
      email, password, first_name, last_name, phone,
      date_of_birth, gender, address, emergency_contact_name,
      emergency_contact_phone, blood_type, allergies, medical_history,
      insurance_provider, insurance_number
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json(
        formatResponse(false, 'User with this email already exists')
      );
    }

    // Get patient role ID
    const roleQuery = 'SELECT id FROM roles WHERE name = ?';
    const roles = await executeQuery(roleQuery, ['patient']);

    // Create user account
    const userId = await User.create({
      email,
      password,
      first_name,
      last_name,
      phone,
      role_id: roles[0].id
    });

    // Create patient record
    const patientResult = await Patient.create({
      user_id: userId,
      date_of_birth,
      gender,
      address,
      emergency_contact_name,
      emergency_contact_phone,
      blood_type,
      allergies,
      medical_history,
      insurance_provider,
      insurance_number
    });

    // Get the created patient with user info
    const newPatient = await Patient.findById(patientResult.id);

    res.status(201).json(
      formatResponse(true, 'Patient created successfully', newPatient)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Update patient
// @route   PUT /api/patients/:id
// @access  Private
const updatePatient = async (req, res, next) => {
  try {
    const patientId = req.params.id;
    
    // Check if patient exists
    const existingPatient = await Patient.findById(patientId);
    if (!existingPatient) {
      return res.status(404).json(
        formatResponse(false, 'Patient not found')
      );
    }

    // Check authorization - patients can only update their own data
    if (req.user.role_name === 'patient') {
      const userPatient = await Patient.findByUserId(req.user.id);
      if (!userPatient || userPatient.id !== parseInt(patientId)) {
        return res.status(403).json(
          formatResponse(false, 'Not authorized to update this patient data')
        );
      }
    }

    const {
      first_name, last_name, phone, date_of_birth, gender, address,
      emergency_contact_name, emergency_contact_phone, blood_type,
      allergies, medical_history, insurance_provider, insurance_number
    } = req.body;

    // Update user information if provided
    if (first_name || last_name || phone) {
      const userUpdateData = {};
      if (first_name) userUpdateData.first_name = first_name;
      if (last_name) userUpdateData.last_name = last_name;
      if (phone) userUpdateData.phone = phone;

      await User.update(existingPatient.user_id, userUpdateData);
    }

    // Update patient information
    const patientUpdateData = {};
    if (date_of_birth) patientUpdateData.date_of_birth = date_of_birth;
    if (gender) patientUpdateData.gender = gender;
    if (address) patientUpdateData.address = address;
    if (emergency_contact_name) patientUpdateData.emergency_contact_name = emergency_contact_name;
    if (emergency_contact_phone) patientUpdateData.emergency_contact_phone = emergency_contact_phone;
    if (blood_type) patientUpdateData.blood_type = blood_type;
    if (allergies !== undefined) patientUpdateData.allergies = allergies;
    if (medical_history !== undefined) patientUpdateData.medical_history = medical_history;
    if (insurance_provider !== undefined) patientUpdateData.insurance_provider = insurance_provider;
    if (insurance_number !== undefined) patientUpdateData.insurance_number = insurance_number;

    if (Object.keys(patientUpdateData).length > 0) {
      await Patient.update(patientId, patientUpdateData);
    }

    // Get updated patient
    const updatedPatient = await Patient.findById(patientId);

    res.json(
      formatResponse(true, 'Patient updated successfully', updatedPatient)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Delete patient
// @route   DELETE /api/patients/:id
// @access  Private (Admin only)
const deletePatient = async (req, res, next) => {
  try {
    const patientId = req.params.id;
    
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json(
        formatResponse(false, 'Patient not found')
      );
    }

    // Soft delete patient
    await Patient.delete(patientId);
    
    // Also deactivate user account
    await User.update(patient.user_id, { status: 'inactive' });

    res.json(
      formatResponse(true, 'Patient deleted successfully')
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get patient medical summary
// @route   GET /api/patients/:id/summary
// @access  Private
const getPatientSummary = async (req, res, next) => {
  try {
    const patientId = req.params.id;

    // Check authorization
    if (req.user.role_name === 'patient') {
      const userPatient = await Patient.findByUserId(req.user.id);
      if (!userPatient || userPatient.id !== parseInt(patientId)) {
        return res.status(403).json(
          formatResponse(false, 'Not authorized to access this patient data')
        );
      }
    }

    const summary = await Patient.getMedicalSummary(patientId);
    
    if (!summary) {
      return res.status(404).json(
        formatResponse(false, 'Patient not found')
      );
    }

    res.json(
      formatResponse(true, 'Patient summary retrieved successfully', summary)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Search patients
// @route   GET /api/patients/search
// @access  Private (Admin, Doctor, Nurse)
const searchPatients = async (req, res, next) => {
  try {
    const { q, limit = 10 } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json(
        formatResponse(false, 'Search query must be at least 2 characters')
      );
    }

    const patients = await Patient.search(q.trim(), parseInt(limit));

    res.json(
      formatResponse(true, 'Search completed successfully', patients)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get patients by doctor
// @route   GET /api/patients/doctor/:doctorId
// @access  Private (Doctor, Admin)
const getPatientsByDoctor = async (req, res, next) => {
  try {
    const { doctorId } = req.params;
    const { page = 0, size = 10 } = req.query;
    const { limit, offset } = getPagination(page, size);

    // Check authorization - doctors can only view their own patients
    if (req.user.role_name === 'doctor' && req.user.doctor_id !== parseInt(doctorId)) {
      return res.status(403).json(
        formatResponse(false, 'Not authorized to access this doctor\'s patients')
      );
    }

    const patients = await Patient.findByDoctor(doctorId, Math.floor(offset / limit), limit);

    res.json(
      formatResponse(true, 'Doctor\'s patients retrieved successfully', patients)
    );
  } catch (error) {
    next(error);
  }
};

// @desc    Get patient statistics
// @route   GET /api/patients/statistics
// @access  Private (Admin, Doctor)
const getPatientStatistics = async (req, res, next) => {
  try {
    const statistics = await Patient.getStatistics();

    res.json(
      formatResponse(true, 'Patient statistics retrieved successfully', statistics)
    );
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  getPatientSummary,
  searchPatients,
  getPatientsByDoctor,
  getPatientStatistics
};

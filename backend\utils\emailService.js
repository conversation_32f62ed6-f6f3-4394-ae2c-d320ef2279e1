const nodemailer = require('nodemailer');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });
};

// Send email
const sendEmail = async (options) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
      to: options.email,
      subject: options.subject,
      html: options.html || options.message,
      text: options.text
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Email sending failed:', error);
    throw error;
  }
};

// Email templates
const emailTemplates = {
  // Welcome email for new patients
  welcomePatient: (patientName, patientId) => ({
    subject: 'Welcome to HealthCare Plus',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #2563eb; color: white; padding: 20px; text-align: center;">
          <h1>Welcome to HealthCare Plus</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${patientName},</h2>
          <p>Welcome to HealthCare Plus! We're excited to have you as our patient.</p>
          <p><strong>Your Patient ID:</strong> ${patientId}</p>
          <p>You can use this ID to book appointments and access your medical records through our patient portal.</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>What's Next?</h3>
            <ul>
              <li>Complete your medical history in the patient portal</li>
              <li>Upload your insurance information</li>
              <li>Book your first appointment</li>
            </ul>
          </div>
          <p>If you have any questions, please don't hesitate to contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  }),

  // Appointment confirmation
  appointmentConfirmation: (patientName, doctorName, appointmentDate, appointmentTime, appointmentId) => ({
    subject: 'Appointment Confirmation - HealthCare Plus',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #10b981; color: white; padding: 20px; text-align: center;">
          <h1>Appointment Confirmed</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${patientName},</h2>
          <p>Your appointment has been confirmed. Here are the details:</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Appointment ID:</strong> ${appointmentId}</p>
            <p><strong>Doctor:</strong> ${doctorName}</p>
            <p><strong>Date:</strong> ${appointmentDate}</p>
            <p><strong>Time:</strong> ${appointmentTime}</p>
          </div>
          <div style="background-color: #fef3c7; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Important Reminders:</h3>
            <ul>
              <li>Please arrive 15 minutes before your appointment</li>
              <li>Bring a valid ID and insurance card</li>
              <li>Bring any relevant medical records or test results</li>
            </ul>
          </div>
          <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  }),

  // Appointment reminder
  appointmentReminder: (patientName, doctorName, appointmentDate, appointmentTime) => ({
    subject: 'Appointment Reminder - Tomorrow',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f59e0b; color: white; padding: 20px; text-align: center;">
          <h1>Appointment Reminder</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${patientName},</h2>
          <p>This is a friendly reminder about your upcoming appointment:</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Doctor:</strong> ${doctorName}</p>
            <p><strong>Date:</strong> ${appointmentDate}</p>
            <p><strong>Time:</strong> ${appointmentTime}</p>
          </div>
          <p>Please arrive 15 minutes early and bring your ID and insurance card.</p>
          <p>See you tomorrow!</p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  }),

  // Password reset
  passwordReset: (name, resetToken, resetUrl) => ({
    subject: 'Password Reset Request - HealthCare Plus',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #dc2626; color: white; padding: 20px; text-align: center;">
          <h1>Password Reset Request</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${name},</h2>
          <p>You have requested to reset your password. Click the button below to reset it:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Reset Password</a>
          </div>
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #6b7280;">${resetUrl}</p>
          <p><strong>This link will expire in 1 hour.</strong></p>
          <p>If you didn't request this password reset, please ignore this email.</p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  }),

  // Lab results ready
  labResultsReady: (patientName, testName, doctorName) => ({
    subject: 'Lab Results Ready - HealthCare Plus',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #7c3aed; color: white; padding: 20px; text-align: center;">
          <h1>Lab Results Ready</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${patientName},</h2>
          <p>Your lab results are now available:</p>
          <div style="background-color: #f3f4f6; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Test:</strong> ${testName}</p>
            <p><strong>Ordered by:</strong> ${doctorName}</p>
          </div>
          <p>Please log in to your patient portal to view your results, or contact your doctor's office.</p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  }),

  // Contact form response
  contactFormResponse: (name, subject) => ({
    subject: 'We received your message - HealthCare Plus',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #2563eb; color: white; padding: 20px; text-align: center;">
          <h1>Message Received</h1>
        </div>
        <div style="padding: 20px;">
          <h2>Hello ${name},</h2>
          <p>Thank you for contacting HealthCare Plus. We have received your message regarding: <strong>${subject}</strong></p>
          <p>Our team will review your message and respond within 24-48 hours during business days.</p>
          <p>For urgent matters, please call our main number: <strong>+1-************</strong></p>
          <p>Best regards,<br>HealthCare Plus Team</p>
        </div>
      </div>
    `
  })
};

// Send specific email types
const sendWelcomeEmail = async (email, patientName, patientId) => {
  const template = emailTemplates.welcomePatient(patientName, patientId);
  return await sendEmail({ email, ...template });
};

const sendAppointmentConfirmation = async (email, patientName, doctorName, appointmentDate, appointmentTime, appointmentId) => {
  const template = emailTemplates.appointmentConfirmation(patientName, doctorName, appointmentDate, appointmentTime, appointmentId);
  return await sendEmail({ email, ...template });
};

const sendAppointmentReminder = async (email, patientName, doctorName, appointmentDate, appointmentTime) => {
  const template = emailTemplates.appointmentReminder(patientName, doctorName, appointmentDate, appointmentTime);
  return await sendEmail({ email, ...template });
};

const sendPasswordResetEmail = async (email, name, resetToken) => {
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;
  const template = emailTemplates.passwordReset(name, resetToken, resetUrl);
  return await sendEmail({ email, ...template });
};

const sendLabResultsNotification = async (email, patientName, testName, doctorName) => {
  const template = emailTemplates.labResultsReady(patientName, testName, doctorName);
  return await sendEmail({ email, ...template });
};

const sendContactFormResponse = async (email, name, subject) => {
  const template = emailTemplates.contactFormResponse(name, subject);
  return await sendEmail({ email, ...template });
};

module.exports = {
  sendEmail,
  sendWelcomeEmail,
  sendAppointmentConfirmation,
  sendAppointmentReminder,
  sendPasswordResetEmail,
  sendLabResultsNotification,
  sendContactFormResponse
};

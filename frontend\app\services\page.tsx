"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  Brain,
  Baby,
  Bone,
  Eye,
  Stethoscope,
  Activity,
  Zap,
  Shield,
  Users,
  Clock,
  Award,
  Calendar,
  Phone,
} from "lucide-react"
import { motion } from "framer-motion"
import { Navigation } from "@/components/navigation"
import Link from "next/link"

const services = [
  {
    title: "Cardiology",
    description: "Comprehensive heart care with advanced cardiac procedures and interventions",
    icon: Heart,
    color: "bg-red-50 dark:bg-red-950 text-red-600 dark:text-red-400",
    features: ["Cardiac Catheterization", "Echocardiography", "Stress Testing", "Pacemaker Implantation"],
    doctors: "8 Specialists",
    availability: "24/7 Emergency",
  },
  {
    title: "Neurology",
    description: "Expert diagnosis and treatment of neurological disorders and brain conditions",
    icon: Brain,
    color: "bg-purple-50 dark:bg-purple-950 text-purple-600 dark:text-purple-400",
    features: ["MRI/CT Imaging", "EEG Testing", "Stroke Care", "Epilepsy Treatment"],
    doctors: "6 Specialists",
    availability: "Mon-Sat",
  },
  {
    title: "Pediatrics",
    description: "Specialized care for infants, children, and adolescents with compassionate approach",
    icon: Baby,
    color: "bg-green-50 dark:bg-green-950 text-green-600 dark:text-green-400",
    features: ["Newborn Care", "Vaccination", "Growth Monitoring", "Pediatric Surgery"],
    doctors: "10 Specialists",
    availability: "24/7",
  },
  {
    title: "Orthopedics",
    description: "Advanced treatment for bone, joint, and muscle conditions with surgical expertise",
    icon: Bone,
    color: "bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400",
    features: ["Joint Replacement", "Sports Medicine", "Fracture Care", "Arthroscopy"],
    doctors: "7 Specialists",
    availability: "Mon-Fri",
  },
  {
    title: "Ophthalmology",
    description: "Complete eye care services from routine exams to complex surgical procedures",
    icon: Eye,
    color: "bg-yellow-50 dark:bg-yellow-950 text-yellow-600 dark:text-yellow-400",
    features: ["Cataract Surgery", "Retinal Care", "Glaucoma Treatment", "LASIK Surgery"],
    doctors: "5 Specialists",
    availability: "Mon-Sat",
  },
  {
    title: "Internal Medicine",
    description: "Primary care and management of adult diseases with preventive healthcare focus",
    icon: Stethoscope,
    color: "bg-indigo-50 dark:bg-indigo-950 text-indigo-600 dark:text-indigo-400",
    features: ["Health Screenings", "Chronic Disease Management", "Preventive Care", "Annual Checkups"],
    doctors: "12 Specialists",
    availability: "Mon-Sat",
  },
  {
    title: "Emergency Medicine",
    description: "24/7 emergency care with rapid response and life-saving interventions",
    icon: Activity,
    color: "bg-red-50 dark:bg-red-950 text-red-600 dark:text-red-400",
    features: ["Trauma Care", "Critical Care", "Emergency Surgery", "Ambulance Service"],
    doctors: "15 Specialists",
    availability: "24/7",
  },
  {
    title: "Radiology",
    description: "Advanced imaging services with state-of-the-art equipment and expert interpretation",
    icon: Zap,
    color: "bg-orange-50 dark:bg-orange-950 text-orange-600 dark:text-orange-400",
    features: ["MRI", "CT Scan", "X-Ray", "Ultrasound"],
    doctors: "4 Specialists",
    availability: "24/7",
  },
  {
    title: "Oncology",
    description: "Comprehensive cancer care with multidisciplinary approach and advanced treatments",
    icon: Shield,
    color: "bg-pink-50 dark:bg-pink-950 text-pink-600 dark:text-pink-400",
    features: ["Chemotherapy", "Radiation Therapy", "Surgical Oncology", "Palliative Care"],
    doctors: "6 Specialists",
    availability: "Mon-Fri",
  },
]

const emergencyServices = [
  "24/7 Emergency Department",
  "Trauma Center Level II",
  "Cardiac Emergency Care",
  "Stroke Response Team",
  "Pediatric Emergency Care",
  "Ambulance Services",
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 px-4 py-2 mb-4">
              Medical Services
            </Badge>
            <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Comprehensive <span className="text-blue-600 dark:text-blue-400">Healthcare Services</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              From routine checkups to complex procedures, we offer a full spectrum of medical services with
              cutting-edge technology and compassionate care.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm h-full group">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-4">
                      <div
                        className={`w-12 h-12 rounded-xl ${service.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                      >
                        <service.icon className="h-6 w-6" />
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {service.availability}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl text-gray-900 dark:text-white">{service.title}</CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-300">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Key Services:</h4>
                      <ul className="space-y-1">
                        {service.features.map((feature, idx) => (
                          <li key={idx} className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                            <div className="w-1.5 h-1.5 bg-blue-600 dark:bg-blue-400 rounded-full mr-2"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                      <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                        <Users className="h-4 w-4 mr-1" />
                        {service.doctors}
                      </div>
                      <Button asChild size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                        <Link href="/appointments">
                          <Calendar className="h-4 w-4 mr-1" />
                          Book Now
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Emergency Services */}
      <section className="py-16 bg-red-50 dark:bg-red-950/20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Emergency Services</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Round-the-clock emergency care when you need it most
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div initial={{ opacity: 0, x: -20 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.6 }}>
              <Card className="border-0 shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                <CardHeader>
                  <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-xl flex items-center justify-center mb-4">
                    <Activity className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <CardTitle className="text-2xl text-gray-900 dark:text-white">24/7 Emergency Care</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    Our emergency department is always ready to provide immediate, life-saving care.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {emergencyServices.map((service, index) => (
                      <div key={index} className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                        <div className="w-2 h-2 bg-red-600 dark:bg-red-400 rounded-full mr-2"></div>
                        {service}
                      </div>
                    ))}
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <Button className="bg-red-600 hover:bg-red-700 text-white flex-1">
                      <Phone className="mr-2 h-4 w-4" />
                      Emergency: 911
                    </Button>
                    <Button
                      variant="outline"
                      className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 flex-1"
                    >
                      <Phone className="mr-2 h-4 w-4" />
                      Hospital: ******-567-8900
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <img
                src="/placeholder.svg?height=400&width=600"
                alt="Emergency Department"
                className="w-full h-80 object-cover rounded-2xl shadow-xl"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Quality & Accreditation */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">Quality & Accreditation</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              Recognized for excellence in healthcare delivery and patient safety
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">JCI Accredited</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Joint Commission International accreditation for quality and patient safety
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-8 w-8 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">ISO Certified</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    ISO 9001:2015 certification for quality management systems
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="border-0 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Clock className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">NABH Accredited</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    National Accreditation Board for Hospitals & Healthcare Providers
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <h2 className="text-4xl font-bold mb-4">Need Medical Care?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Our experienced medical team is here to provide you with the best possible care
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-blue-50">
                <Link href="/appointments">
                  <Calendar className="mr-2 h-5 w-5" />
                  Schedule Appointment
                </Link>
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                <Link href="/doctors">
                  <Users className="mr-2 h-5 w-5" />
                  Find a Doctor
                </Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Eye, Edit, Download, FileText, Calendar, User, Activity } from "lucide-react"
import { motion } from "framer-motion"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { DoctorHeader } from "@/components/doctor-header"

const medicalRecords = [
  {
    id: "MR001",
    patientId: "P001",
    patientName: "<PERSON>",
    patientAge: 45,
    recordType: "Consultation",
    date: "2024-01-20",
    diagnosis: "Hypertension Stage 1",
    treatment: "Lifestyle modifications, ACE inhibitor prescribed",
    notes: "Patient responding well to treatment. Blood pressure improved from 150/95 to 135/85.",
    followUp: "2024-02-20",
    status: "active",
    attachments: ["blood_test_results.pdf", "ecg_report.pdf"],
  },
  {
    id: "MR002",
    patientId: "P002",
    patientName: "Sarah Johnson",
    patientAge: 32,
    recordType: "Follow-up",
    date: "2024-01-19",
    diagnosis: "Type 2 Diabetes Mellitus",
    treatment: "Metformin 500mg twice daily, dietary counseling",
    notes: "HbA1c improved from 8.2% to 7.1%. Patient adhering to medication and diet plan.",
    followUp: "2024-03-19",
    status: "active",
    attachments: ["glucose_log.pdf", "diet_plan.pdf"],
  },
  {
    id: "MR003",
    patientId: "P003",
    patientName: "Michael Brown",
    patientAge: 58,
    recordType: "Surgery",
    date: "2024-01-18",
    diagnosis: "Coronary Artery Disease",
    treatment: "Percutaneous Coronary Intervention (PCI) with stent placement",
    notes: "Successful PCI of LAD with drug-eluting stent. Patient stable post-procedure.",
    followUp: "2024-01-25",
    status: "completed",
    attachments: ["surgery_report.pdf", "post_op_instructions.pdf"],
  },
  {
    id: "MR004",
    patientId: "P004",
    patientName: "Emily Davis",
    patientAge: 28,
    recordType: "Emergency",
    date: "2024-01-17",
    diagnosis: "Acute Migraine",
    treatment: "IV hydration, sumatriptan injection, rest in dark room",
    notes: "Patient presented with severe headache, photophobia. Responded well to treatment.",
    followUp: "2024-01-24",
    status: "completed",
    attachments: ["ct_scan.pdf"],
  },
]

const recentRecords = medicalRecords.slice(0, 3)
const activeRecords = medicalRecords.filter((record) => record.status === "active")

export default function DoctorRecordsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedRecord, setSelectedRecord] = useState(null)

  const filteredRecords = medicalRecords.filter(
    (record) =>
      record.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.diagnosis.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.recordType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.id.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Medical Records</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage and review patient medical records and treatment history
              </p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              New Record
            </Button>
          </div>

          <Tabs defaultValue="all" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 lg:w-96">
              <TabsTrigger value="all">All Records ({medicalRecords.length})</TabsTrigger>
              <TabsTrigger value="recent">Recent (3)</TabsTrigger>
              <TabsTrigger value="active">Active ({activeRecords.length})</TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="space-y-6">
              {/* Search */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardContent className="p-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search records by patient name, diagnosis, or record ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Records Table */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">All Medical Records</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Complete list of patient medical records
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Patient</TableHead>
                        <TableHead>Record Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Diagnosis</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Follow-up</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRecords.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src="/placeholder.svg?height=40&width=40" alt={record.patientName} />
                                <AvatarFallback>
                                  {record.patientName
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <p className="font-medium text-gray-900 dark:text-white">{record.patientName}</p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {record.patientAge} years • {record.patientId}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                record.recordType === "Emergency"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  : record.recordType === "Surgery"
                                    ? "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                                    : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                              }
                            >
                              {record.recordType}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{record.date}</span>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium text-gray-900 dark:text-white">{record.diagnosis}</span>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="secondary"
                              className={
                                record.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                              }
                            >
                              {record.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="text-gray-600 dark:text-gray-400">{record.followUp}</span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-4xl">
                                  <DialogHeader>
                                    <DialogTitle>Medical Record - {record.patientName}</DialogTitle>
                                    <DialogDescription>
                                      Detailed medical record information and treatment history
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="grid grid-cols-2 gap-6 py-4">
                                    <div className="space-y-4">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Patient Information
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Name:</span> {record.patientName}
                                          </p>
                                          <p>
                                            <span className="font-medium">Age:</span> {record.patientAge} years
                                          </p>
                                          <p>
                                            <span className="font-medium">Patient ID:</span> {record.patientId}
                                          </p>
                                          <p>
                                            <span className="font-medium">Record ID:</span> {record.id}
                                          </p>
                                        </div>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Medical Details
                                        </h4>
                                        <div className="space-y-2 text-sm">
                                          <p>
                                            <span className="font-medium">Record Type:</span> {record.recordType}
                                          </p>
                                          <p>
                                            <span className="font-medium">Date:</span> {record.date}
                                          </p>
                                          <p>
                                            <span className="font-medium">Diagnosis:</span> {record.diagnosis}
                                          </p>
                                          <p>
                                            <span className="font-medium">Status:</span> {record.status}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="space-y-4">
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Treatment</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">{record.treatment}</p>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Notes</h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">{record.notes}</p>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                          Follow-up Date
                                        </h4>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">{record.followUp}</p>
                                      </div>
                                      <div>
                                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Attachments</h4>
                                        <div className="space-y-2">
                                          {record.attachments.map((attachment, index) => (
                                            <div
                                              key={index}
                                              className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
                                            >
                                              <span className="text-sm">{attachment}</span>
                                              <Button size="sm" variant="ghost">
                                                <Download className="h-4 w-4" />
                                              </Button>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                              <Button size="sm" variant="outline">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button size="sm" variant="outline">
                                <Download className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="recent" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Recent Medical Records</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Most recently created or updated records
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentRecords.map((record, index) => (
                      <motion.div
                        key={record.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="p-6 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">{record.patientName}</h3>
                                <Badge
                                  variant="secondary"
                                  className={
                                    record.recordType === "Emergency"
                                      ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                      : record.recordType === "Surgery"
                                        ? "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
                                        : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                  }
                                >
                                  {record.recordType}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{record.diagnosis}</p>
                              <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-500">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {record.date}
                                </div>
                                <div className="flex items-center">
                                  <User className="h-3 w-3 mr-1" />
                                  {record.patientId}
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge
                              className={
                                record.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                              }
                            >
                              {record.status}
                            </Badge>
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="active" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-gray-900 dark:text-white">Active Medical Records</CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Records requiring ongoing treatment or follow-up
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {activeRecords.map((record, index) => (
                      <div
                        key={record.id}
                        className="p-6 rounded-lg border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                              <Activity className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h3 className="font-semibold text-gray-900 dark:text-white">{record.patientName}</h3>
                                <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  {record.recordType}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{record.diagnosis}</p>
                              <p className="text-xs text-green-600 dark:text-green-400">
                                Next follow-up: {record.followUp}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bell, Shield, Clock, User, Mail, Save, Eye, EyeOff, Smartphone } from "lucide-react"
import { DoctorSidebar } from "@/components/doctor-sidebar"
import { DoctorHeader } from "@/components/doctor-header"

const settingsData = {
  profile: {
    name: "<PERSON>. <PERSON>",
    email: "<EMAIL>",
    phone: "+****************",
    specialization: "Cardiology",
    licenseNumber: "MD123456789",
    workingHours: {
      start: "09:00",
      end: "17:00",
    },
    consultationDuration: "30",
    breakDuration: "15",
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    appointmentReminders: true,
    labResultAlerts: true,
    emergencyAlerts: true,
    messageNotifications: true,
    scheduleChanges: true,
  },
  privacy: {
    profileVisibility: "colleagues",
    showOnlineStatus: true,
    allowDirectMessages: true,
    shareSchedule: false,
  },
  appearance: {
    theme: "system",
    language: "english",
    timezone: "EST",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12-hour",
  },
}

export default function DoctorSettingsPage() {
  const [settings, setSettings] = useState(settingsData)
  const [showPassword, setShowPassword] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }))
    setHasChanges(true)
  }

  const handleSave = () => {
    // Handle save logic here
    console.log("Settings saved:", settings)
    setHasChanges(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Settings</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Manage your account preferences and system settings
              </p>
            </div>
            {hasChanges && (
              <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700 text-white">
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            )}
          </div>

          <Tabs defaultValue="profile" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 lg:w-96">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="notifications">Notifications</TabsTrigger>
              <TabsTrigger value="privacy">Privacy</TabsTrigger>
              <TabsTrigger value="appearance">Appearance</TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-6">
              <div className="grid lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2 space-y-6">
                  {/* Personal Information */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="flex items-center text-gray-900 dark:text-white">
                        <User className="h-5 w-5 mr-2" />
                        Personal Information
                      </CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Update your personal and professional details
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name</Label>
                          <Input
                            id="name"
                            value={settings.profile.name}
                            onChange={(e) => handleSettingChange("profile", "name", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="specialization">Specialization</Label>
                          <Input
                            id="specialization"
                            value={settings.profile.specialization}
                            onChange={(e) => handleSettingChange("profile", "specialization", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address</Label>
                          <Input
                            id="email"
                            type="email"
                            value={settings.profile.email}
                            onChange={(e) => handleSettingChange("profile", "email", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone Number</Label>
                          <Input
                            id="phone"
                            value={settings.profile.phone}
                            onChange={(e) => handleSettingChange("profile", "phone", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="license">License Number</Label>
                          <Input
                            id="license"
                            value={settings.profile.licenseNumber}
                            onChange={(e) => handleSettingChange("profile", "licenseNumber", e.target.value)}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Working Hours */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="flex items-center text-gray-900 dark:text-white">
                        <Clock className="h-5 w-5 mr-2" />
                        Working Hours & Schedule
                      </CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Configure your default working hours and appointment settings
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="startTime">Start Time</Label>
                          <Input
                            id="startTime"
                            type="time"
                            value={settings.profile.workingHours.start}
                            onChange={(e) =>
                              handleSettingChange("profile", "workingHours", {
                                ...settings.profile.workingHours,
                                start: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endTime">End Time</Label>
                          <Input
                            id="endTime"
                            type="time"
                            value={settings.profile.workingHours.end}
                            onChange={(e) =>
                              handleSettingChange("profile", "workingHours", {
                                ...settings.profile.workingHours,
                                end: e.target.value,
                              })
                            }
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="consultationDuration">Default Consultation Duration (minutes)</Label>
                          <Select
                            value={settings.profile.consultationDuration}
                            onValueChange={(value) => handleSettingChange("profile", "consultationDuration", value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="15">15 minutes</SelectItem>
                              <SelectItem value="30">30 minutes</SelectItem>
                              <SelectItem value="45">45 minutes</SelectItem>
                              <SelectItem value="60">60 minutes</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="breakDuration">Break Duration (minutes)</Label>
                          <Select
                            value={settings.profile.breakDuration}
                            onValueChange={(value) => handleSettingChange("profile", "breakDuration", value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="10">10 minutes</SelectItem>
                              <SelectItem value="15">15 minutes</SelectItem>
                              <SelectItem value="30">30 minutes</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Security */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="flex items-center text-gray-900 dark:text-white">
                        <Shield className="h-5 w-5 mr-2" />
                        Security
                      </CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Manage your account security settings
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="currentPassword">Current Password</Label>
                          <div className="relative">
                            <Input
                              id="currentPassword"
                              type={showPassword ? "text" : "password"}
                              placeholder="Enter current password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="newPassword">New Password</Label>
                          <Input id="newPassword" type="password" placeholder="Enter new password" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword">Confirm New Password</Label>
                          <Input id="confirmPassword" type="password" placeholder="Confirm new password" />
                        </div>
                        <Button variant="outline" className="bg-transparent">
                          Update Password
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Profile Picture */}
                <div className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Profile Picture</CardTitle>
                    </CardHeader>
                    <CardContent className="text-center">
                      <Avatar className="h-32 w-32 mx-auto mb-4">
                        <AvatarImage src="/placeholder.svg?height=128&width=128" alt="Dr. Sarah Williams" />
                        <AvatarFallback className="text-2xl">SW</AvatarFallback>
                      </Avatar>
                      <Button variant="outline" className="bg-transparent">
                        Change Picture
                      </Button>
                    </CardContent>
                  </Card>

                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Account Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Account Type</span>
                          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            Professional
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Verification Status</span>
                          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            Verified
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600 dark:text-gray-400">Member Since</span>
                          <span className="text-sm text-gray-900 dark:text-white">January 2020</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <Bell className="h-5 w-5 mr-2" />
                    Notification Preferences
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-400">
                    Choose how you want to receive notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Mail className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">Email Notifications</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Receive notifications via email
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.notifications.emailNotifications}
                        onCheckedChange={(checked) =>
                          handleSettingChange("notifications", "emailNotifications", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Smartphone className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">SMS Notifications</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Receive notifications via text message
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.notifications.smsNotifications}
                        onCheckedChange={(checked) =>
                          handleSettingChange("notifications", "smsNotifications", checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Bell className="h-5 w-5 text-gray-500" />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">Push Notifications</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Receive push notifications in browser
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={settings.notifications.pushNotifications}
                        onCheckedChange={(checked) =>
                          handleSettingChange("notifications", "pushNotifications", checked)
                        }
                      />
                    </div>

                    <div className="border-t pt-6">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-4">Specific Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">Appointment Reminders</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Get reminded about upcoming appointments
                            </p>
                          </div>
                          <Switch
                            checked={settings.notifications.appointmentReminders}
                            onCheckedChange={(checked) =>
                              handleSettingChange("notifications", "appointmentReminders", checked)
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">Lab Result Alerts</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Get notified when lab results are available
                            </p>
                          </div>
                          <Switch
                            checked={settings.notifications.labResultAlerts}
                            onCheckedChange={(checked) =>
                              handleSettingChange("notifications", "labResultAlerts", checked)
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">Emergency Alerts</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Receive urgent emergency notifications
                            </p>
                          </div>
                          <Switch
                            checked={settings.notifications.emergencyAlerts}
                            onCheckedChange={(checked) =>
                              handleSettingChange("notifications", "emergencyAlerts", checked)
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">Message Notifications</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Get notified about new messages
                            </p>
                          </div>
                          <Switch
                            checked={settings.notifications.messageNotifications}
                            onCheckedChange={(checked) =>
                              handleSettingChange("notifications", "messageNotifications", checked)
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">Schedule Changes</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Get notified about schedule modifications
                            </p>
                          </div>
                          <Switch
                            checked={settings.notifications.scheduleChanges}
                            onCheckedChange={(checked) =>
                              handleSettingChange("notifications", "scheduleChanges", checked)
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="privacy" className="space-y-6">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark\

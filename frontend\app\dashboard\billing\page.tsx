"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Plus,
  DollarSign,
  CreditCard,
  FileText,
  Download,
  Eye,
  Edit,
  Send,
  AlertCircle,
  CheckCircle,
  Clock,
  TrendingUp,
} from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const invoices = [
  {
    id: "INV-2024-001",
    patientName: "<PERSON>",
    patientId: "P001",
    date: "2024-01-20",
    dueDate: "2024-02-20",
    amount: 1250.0,
    paid: 1250.0,
    balance: 0.0,
    status: "Paid",
    services: [
      { name: "Cardiology Consultation", quantity: 1, rate: 300.0, amount: 300.0 },
      { name: "ECG Test", quantity: 1, rate: 150.0, amount: 150.0 },
      { name: "Blood Work", quantity: 1, rate: 200.0, amount: 200.0 },
      { name: "Medication", quantity: 1, rate: 600.0, amount: 600.0 },
    ],
    insurance: "Blue Cross Blue Shield",
    insuranceClaim: "CLM-2024-001",
    paymentMethod: "Insurance + Credit Card",
  },
  {
    id: "INV-2024-002",
    patientName: "Sarah Johnson",
    patientId: "P002",
    date: "2024-01-22",
    dueDate: "2024-02-22",
    amount: 850.0,
    paid: 0.0,
    balance: 850.0,
    status: "Pending",
    services: [
      { name: "Pediatric Consultation", quantity: 1, rate: 250.0, amount: 250.0 },
      { name: "Vaccination", quantity: 3, rate: 100.0, amount: 300.0 },
      { name: "Growth Assessment", quantity: 1, rate: 150.0, amount: 150.0 },
      { name: "Lab Tests", quantity: 1, rate: 150.0, amount: 150.0 },
    ],
    insurance: "Aetna",
    insuranceClaim: "CLM-2024-002",
    paymentMethod: "Pending",
  },
  {
    id: "INV-2024-003",
    patientName: "Michael Brown",
    patientId: "P003",
    date: "2024-01-18",
    dueDate: "2024-02-18",
    amount: 3200.0,
    paid: 2000.0,
    balance: 1200.0,
    status: "Partial",
    services: [
      { name: "Orthopedic Surgery", quantity: 1, rate: 2500.0, amount: 2500.0 },
      { name: "Anesthesia", quantity: 1, rate: 400.0, amount: 400.0 },
      { name: "Post-op Care", quantity: 2, rate: 150.0, amount: 300.0 },
    ],
    insurance: "Medicare",
    insuranceClaim: "CLM-2024-003",
    paymentMethod: "Insurance + Cash",
  },
  {
    id: "INV-2024-004",
    patientName: "Emily Davis",
    patientId: "P004",
    date: "2024-01-25",
    dueDate: "2024-02-25",
    amount: 450.0,
    paid: 0.0,
    balance: 450.0,
    status: "Overdue",
    services: [
      { name: "Dermatology Consultation", quantity: 1, rate: 200.0, amount: 200.0 },
      { name: "Skin Biopsy", quantity: 1, rate: 250.0, amount: 250.0 },
    ],
    insurance: "Cigna",
    insuranceClaim: "CLM-2024-004",
    paymentMethod: "Pending",
  },
]

const statusColors = {
  Paid: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  Partial: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  Overdue: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
}

const stats = [
  {
    title: "Total Revenue",
    value: "$284,750",
    change: "+15%",
    trend: "up",
    icon: DollarSign,
    color: "text-green-600 dark:text-green-400",
  },
  {
    title: "Outstanding",
    value: "$45,200",
    change: "-8%",
    trend: "down",
    icon: Clock,
    color: "text-orange-600 dark:text-orange-400",
  },
  {
    title: "Paid Today",
    value: "$12,450",
    change: "+22%",
    trend: "up",
    icon: CheckCircle,
    color: "text-blue-600 dark:text-blue-400",
  },
  {
    title: "Overdue",
    value: "$8,900",
    change: "+5%",
    trend: "up",
    icon: AlertCircle,
    color: "text-red-600 dark:text-red-400",
  },
]

export default function BillingPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [selectedInvoice, setSelectedInvoice] = useState<(typeof invoices)[0] | null>(null)

  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "All" || invoice.status === statusFilter

    return matchesSearch && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Billing & Invoices</h1>
              <p className="text-gray-600 dark:text-gray-400">Manage patient billing, payments, and insurance claims</p>
            </div>
            <div className="flex gap-3 mt-4 sm:mt-0">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                New Invoice
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                        <div className="flex items-center mt-2">
                          <TrendingUp
                            className={`h-4 w-4 mr-1 ${stat.trend === "up" ? "text-green-500" : "text-red-500"}`}
                          />
                          <span
                            className={`text-sm font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}
                          >
                            {stat.change}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">from last month</span>
                        </div>
                      </div>
                      <div className={`p-3 rounded-2xl bg-gray-50 dark:bg-gray-700 ${stat.color}`}>
                        <stat.icon className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Filters */}
          <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search invoices by patient name or invoice ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 border-gray-200 dark:border-gray-700"
                  />
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full lg:w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All Status</SelectItem>
                    <SelectItem value="Paid">Paid</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                    <SelectItem value="Partial">Partial</SelectItem>
                    <SelectItem value="Overdue">Overdue</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Invoices Grid */}
          <div className="space-y-4">
            {filteredInvoices.map((invoice, index) => (
              <motion.div
                key={invoice.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row gap-6">
                      {/* Invoice Info */}
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{invoice.id}</h3>
                            <p className="text-gray-600 dark:text-gray-400">
                              {invoice.patientName} • {invoice.patientId}
                            </p>
                          </div>
                          <Badge
                            className={`${statusColors[invoice.status as keyof typeof statusColors]} mt-2 sm:mt-0`}
                          >
                            {invoice.status}
                          </Badge>
                        </div>

                        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Date:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{invoice.date}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Due Date:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{invoice.dueDate}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Total Amount:</span>
                            <p className="font-medium text-gray-900 dark:text-white">${invoice.amount.toFixed(2)}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Balance:</span>
                            <p className={`font-medium ${invoice.balance > 0 ? "text-red-600" : "text-green-600"}`}>
                              ${invoice.balance.toFixed(2)}
                            </p>
                          </div>
                        </div>

                        <div className="mt-4 grid sm:grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Insurance:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{invoice.insurance}</p>
                          </div>
                          <div>
                            <span className="text-gray-600 dark:text-gray-400">Payment Method:</span>
                            <p className="font-medium text-gray-900 dark:text-white">{invoice.paymentMethod}</p>
                          </div>
                        </div>

                        {/* Services Preview */}
                        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">Services:</p>
                          <div className="space-y-1">
                            {invoice.services.slice(0, 2).map((service, idx) => (
                              <div key={idx} className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                                <span>{service.name}</span>
                                <span>${service.amount.toFixed(2)}</span>
                              </div>
                            ))}
                            {invoice.services.length > 2 && (
                              <p className="text-sm text-gray-500">+{invoice.services.length - 2} more services</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-row lg:flex-col gap-2 lg:w-32">
                        <Button
                          size="sm"
                          onClick={() => setSelectedInvoice(invoice)}
                          className="bg-blue-600 hover:bg-blue-700 text-white flex-1 lg:flex-none"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-200 dark:border-gray-700 flex-1 lg:flex-none"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-gray-200 dark:border-gray-700 flex-1 lg:flex-none"
                        >
                          <Send className="h-4 w-4 mr-1" />
                          Send
                        </Button>
                        {invoice.balance > 0 && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-green-200 text-green-600 hover:bg-green-50 dark:border-green-800 dark:text-green-400 flex-1 lg:flex-none"
                          >
                            <CreditCard className="h-4 w-4 mr-1" />
                            Pay
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </main>
      </div>

      {/* Invoice Detail Modal */}
      {selectedInvoice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Invoice Details</h2>
                  <p className="text-gray-600 dark:text-gray-400">{selectedInvoice.id}</p>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setSelectedInvoice(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* Invoice Header */}
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Bill To:</h3>
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">{selectedInvoice.patientName}</p>
                    <p className="text-gray-600 dark:text-gray-400">Patient ID: {selectedInvoice.patientId}</p>
                    <p className="text-gray-600 dark:text-gray-400">Insurance: {selectedInvoice.insurance}</p>
                    <p className="text-gray-600 dark:text-gray-400">Claim: {selectedInvoice.insuranceClaim}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Invoice Info:</h3>
                  <div className="space-y-1 text-sm">
                    <p>
                      <span className="text-gray-600 dark:text-gray-400">Date:</span> {selectedInvoice.date}
                    </p>
                    <p>
                      <span className="text-gray-600 dark:text-gray-400">Due Date:</span> {selectedInvoice.dueDate}
                    </p>
                    <p>
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      <Badge className={`ml-2 ${statusColors[selectedInvoice.status as keyof typeof statusColors]}`}>
                        {selectedInvoice.status}
                      </Badge>
                    </p>
                  </div>
                </div>
              </div>

              {/* Services Table */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Services & Charges:</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white">
                          Service
                        </th>
                        <th className="px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white">Qty</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">Rate</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-900 dark:text-white">
                          Amount
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {selectedInvoice.services.map((service, idx) => (
                        <tr key={idx}>
                          <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">{service.name}</td>
                          <td className="px-4 py-3 text-sm text-center text-gray-600 dark:text-gray-400">
                            {service.quantity}
                          </td>
                          <td className="px-4 py-3 text-sm text-right text-gray-600 dark:text-gray-400">
                            ${service.rate.toFixed(2)}
                          </td>
                          <td className="px-4 py-3 text-sm text-right font-medium text-gray-900 dark:text-white">
                            ${service.amount.toFixed(2)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Payment Summary */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                    <span className="font-medium">${selectedInvoice.amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Paid:</span>
                    <span className="font-medium text-green-600">${selectedInvoice.paid.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t border-gray-200 dark:border-gray-600 pt-2">
                    <span>Balance Due:</span>
                    <span className={selectedInvoice.balance > 0 ? "text-red-600" : "text-green-600"}>
                      ${selectedInvoice.balance.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <FileText className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                <Button variant="outline">
                  <Send className="h-4 w-4 mr-2" />
                  Send Invoice
                </Button>
                {selectedInvoice.balance > 0 && (
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    <CreditCard className="h-4 w-4 mr-2" />
                    Record Payment
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  )
}

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/dashboard', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get dashboard statistics - Coming soon' });
});

router.get('/patients', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get patient reports - Coming soon' });
});

router.get('/appointments', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get appointment reports - Coming soon' });
});

router.get('/billing', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Get billing reports - Coming soon' });
});

router.get('/inventory', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Get inventory reports - Coming soon' });
});

router.get('/staff', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Get staff reports - Coming soon' });
});

router.post('/custom', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Generate custom report - Coming soon' });
});

module.exports = router;

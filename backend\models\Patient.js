const { executeQuery } = require('../config/database');
const { generatePatientId, calculateAge } = require('../utils/helpers');

class Patient {
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.patient_id = data.patient_id;
    this.date_of_birth = data.date_of_birth;
    this.gender = data.gender;
    this.address = data.address;
    this.emergency_contact_name = data.emergency_contact_name;
    this.emergency_contact_phone = data.emergency_contact_phone;
    this.blood_type = data.blood_type;
    this.allergies = data.allergies;
    this.medical_history = data.medical_history;
    this.insurance_provider = data.insurance_provider;
    this.insurance_number = data.insurance_number;
    this.status = data.status || 'active';
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new patient
  static async create(patientData) {
    try {
      const patientId = generatePatientId();
      
      const query = `
        INSERT INTO patients (
          user_id, patient_id, date_of_birth, gender, address,
          emergency_contact_name, emergency_contact_phone, blood_type,
          allergies, medical_history, insurance_provider, insurance_number,
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;
      
      const result = await executeQuery(query, [
        patientData.user_id,
        patientId,
        patientData.date_of_birth,
        patientData.gender,
        patientData.address,
        patientData.emergency_contact_name,
        patientData.emergency_contact_phone,
        patientData.blood_type || null,
        patientData.allergies || null,
        patientData.medical_history || null,
        patientData.insurance_provider || null,
        patientData.insurance_number || null,
        patientData.status || 'active'
      ]);

      return { id: result.insertId, patient_id: patientId };
    } catch (error) {
      throw error;
    }
  }

  // Find patient by ID
  static async findById(id) {
    try {
      const query = `
        SELECT p.*, u.first_name, u.last_name, u.email, u.phone
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.id = ? AND p.status != 'deleted'
      `;
      
      const patients = await executeQuery(query, [id]);
      return patients.length > 0 ? patients[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Find patient by patient ID
  static async findByPatientId(patientId) {
    try {
      const query = `
        SELECT p.*, u.first_name, u.last_name, u.email, u.phone
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.patient_id = ? AND p.status != 'deleted'
      `;
      
      const patients = await executeQuery(query, [patientId]);
      return patients.length > 0 ? patients[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Find patient by user ID
  static async findByUserId(userId) {
    try {
      const query = `
        SELECT p.*, u.first_name, u.last_name, u.email, u.phone
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.user_id = ? AND p.status != 'deleted'
      `;
      
      const patients = await executeQuery(query, [userId]);
      return patients.length > 0 ? patients[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all patients with pagination and filters
  static async findAll(page = 0, limit = 10, filters = {}) {
    try {
      let whereClause = 'WHERE p.status != "deleted"';
      let params = [];

      if (filters.search) {
        whereClause += ' AND (u.first_name LIKE ? OR u.last_name LIKE ? OR p.patient_id LIKE ? OR u.email LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      if (filters.gender) {
        whereClause += ' AND p.gender = ?';
        params.push(filters.gender);
      }

      if (filters.blood_type) {
        whereClause += ' AND p.blood_type = ?';
        params.push(filters.blood_type);
      }

      if (filters.age_min || filters.age_max) {
        if (filters.age_min) {
          whereClause += ' AND TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) >= ?';
          params.push(filters.age_min);
        }
        if (filters.age_max) {
          whereClause += ' AND TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) <= ?';
          params.push(filters.age_max);
        }
      }

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        ${whereClause}
      `;
      
      const countResult = await executeQuery(countQuery, params);
      const total = countResult[0].total;

      // Get patients
      const query = `
        SELECT 
          p.*,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) as age,
          (SELECT COUNT(*) FROM appointments WHERE patient_id = p.id) as total_appointments,
          (SELECT MAX(appointment_date) FROM appointments WHERE patient_id = p.id AND status = 'completed') as last_visit
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        ${whereClause}
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      params.push(limit, page * limit);
      const patients = await executeQuery(query, params);

      return {
        patients,
        total,
        page,
        limit
      };
    } catch (error) {
      throw error;
    }
  }

  // Update patient
  static async update(id, updateData) {
    try {
      const fields = [];
      const params = [];

      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined && key !== 'id' && key !== 'patient_id') {
          fields.push(`${key} = ?`);
          params.push(updateData[key]);
        }
      });

      if (fields.length === 0) {
        throw new Error('No fields to update');
      }

      fields.push('updated_at = NOW()');
      params.push(id);

      const query = `UPDATE patients SET ${fields.join(', ')} WHERE id = ?`;
      const result = await executeQuery(query, params);

      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete patient (soft delete)
  static async delete(id) {
    try {
      const query = 'UPDATE patients SET status = ?, updated_at = NOW() WHERE id = ?';
      const result = await executeQuery(query, ['deleted', id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get patient's medical summary
  static async getMedicalSummary(patientId) {
    try {
      const query = `
        SELECT 
          p.*,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) as age,
          (SELECT COUNT(*) FROM appointments WHERE patient_id = p.id) as total_appointments,
          (SELECT COUNT(*) FROM appointments WHERE patient_id = p.id AND status = 'completed') as completed_appointments,
          (SELECT COUNT(*) FROM prescriptions WHERE patient_id = p.id) as total_prescriptions,
          (SELECT COUNT(*) FROM lab_tests WHERE patient_id = p.id) as total_lab_tests,
          (SELECT MAX(appointment_date) FROM appointments WHERE patient_id = p.id AND status = 'completed') as last_visit,
          (SELECT MIN(appointment_date) FROM appointments WHERE patient_id = p.id) as first_visit
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.id = ? AND p.status != 'deleted'
      `;

      const patients = await executeQuery(query, [patientId]);
      return patients.length > 0 ? patients[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get patient statistics
  static async getStatistics() {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_patients,
          SUM(CASE WHEN p.status = 'active' THEN 1 ELSE 0 END) as active_patients,
          SUM(CASE WHEN p.gender = 'male' THEN 1 ELSE 0 END) as male_patients,
          SUM(CASE WHEN p.gender = 'female' THEN 1 ELSE 0 END) as female_patients,
          AVG(TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE())) as average_age,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) < 18 THEN 1 ELSE 0 END) as pediatric_patients,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) >= 65 THEN 1 ELSE 0 END) as senior_patients
        FROM patients p
        WHERE p.status != 'deleted'
      `;

      const stats = await executeQuery(query);
      return stats[0];
    } catch (error) {
      throw error;
    }
  }

  // Get patients by doctor
  static async findByDoctor(doctorId, page = 0, limit = 10) {
    try {
      const query = `
        SELECT DISTINCT
          p.*,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) as age,
          MAX(a.appointment_date) as last_appointment
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        LEFT JOIN appointments a ON p.id = a.patient_id
        WHERE a.doctor_id = ? AND p.status != 'deleted'
        GROUP BY p.id
        ORDER BY last_appointment DESC
        LIMIT ? OFFSET ?
      `;

      const patients = await executeQuery(query, [doctorId, limit, page * limit]);
      return patients;
    } catch (error) {
      throw error;
    }
  }

  // Search patients
  static async search(searchTerm, limit = 10) {
    try {
      const query = `
        SELECT 
          p.*,
          u.first_name,
          u.last_name,
          u.email,
          u.phone,
          TIMESTAMPDIFF(YEAR, p.date_of_birth, CURDATE()) as age
        FROM patients p
        LEFT JOIN users u ON p.user_id = u.id
        WHERE p.status != 'deleted'
        AND (
          u.first_name LIKE ? OR 
          u.last_name LIKE ? OR 
          p.patient_id LIKE ? OR 
          u.email LIKE ? OR
          u.phone LIKE ?
        )
        ORDER BY u.first_name, u.last_name
        LIMIT ?
      `;

      const searchPattern = `%${searchTerm}%`;
      const patients = await executeQuery(query, [
        searchPattern, searchPattern, searchPattern, searchPattern, searchPattern, limit
      ]);

      return patients;
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Patient;

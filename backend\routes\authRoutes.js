const express = require('express');
const {
  register,
  login,
  getMe,
  refreshToken,
  forgotPassword,
  resetPassword,
  changePassword,
  logout
} = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

// Public routes
router.post('/register', validate(schemas.register), register);
router.post('/login', validate(schemas.login), login);
router.post('/refresh', refreshToken);
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', validate(schemas.resetPassword), resetPassword);

// Protected routes
router.get('/me', protect, getMe);
router.put('/change-password', protect, validate(schemas.changePassword), changePassword);
router.post('/logout', protect, logout);

module.exports = router;

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Search, Plus, CalendarIcon, User, Stethoscope, Download, Eye, Edit, CheckCircle, XCircle } from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

const appointments = [
  {
    id: "APT001",
    patientName: "<PERSON>",
    patientId: "P001",
    doctor<PERSON>ame: "Dr. <PERSON>",
    department: "Cardiology",
    date: "2024-01-25",
    time: "09:00 AM",
    duration: "30 min",
    type: "Follow-up",
    status: "Confirmed",
    reason: "Cardiac check-up and medication review",
    phone: "+1-************",
    email: "<EMAIL>",
    notes: "Patient reports chest pain occasionally",
  },
  {
    id: "APT002",
    patientName: "Sarah Johnson",
    patientId: "P002",
    doctorName: "Dr. Emily Rodriguez",
    department: "Pediatrics",
    date: "2024-01-25",
    time: "10:30 AM",
    duration: "45 min",
    type: "New Patient",
    status: "Pending",
    reason: "Child wellness check and vaccination",
    phone: "+1-************",
    email: "<EMAIL>",
    notes: "First visit, bring vaccination records",
  },
  {
    id: "APT003",
    patientName: "Michael Brown",
    patientId: "P003",
    doctorName: "Dr. James Thompson",
    department: "Orthopedics",
    date: "2024-01-25",
    time: "02:00 PM",
    duration: "60 min",
    type: "Consultation",
    status: "Completed",
    reason: "Knee pain evaluation and treatment plan",
    phone: "+1-************",
    email: "<EMAIL>",
    notes: "X-ray results reviewed, surgery recommended",
  },
  {
    id: "APT004",
    patientName: "Emily Davis",
    patientId: "P004",
    doctorName: "Dr. Lisa Park",
    department: "Dermatology",
    date: "2024-01-26",
    time: "11:00 AM",
    duration: "30 min",
    type: "Follow-up",
    status: "Cancelled",
    reason: "Skin condition follow-up",
    phone: "+1-************",
    email: "<EMAIL>",
    notes: "Patient requested reschedule",
  },
  {
    id: "APT005",
    patientName: "Robert Wilson",
    patientId: "P005",
    doctorName: "Dr. Michael Chen",
    department: "Neurology",
    date: "2024-01-26",
    time: "03:30 PM",
    duration: "45 min",
    type: "Emergency",
    status: "Confirmed",
    reason: "Neurological symptoms evaluation",
    phone: "+1-************",
    email: "<EMAIL>",
    notes: "Urgent consultation required",
  },
]

const statusColors = {
  Confirmed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
  Pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
  Completed: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  Cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
  "No Show": "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
}

const typeColors = {
  "New Patient": "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
  "Follow-up": "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  Consultation: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200",
  Emergency: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
}

export default function AppointmentsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("All")
  const [departmentFilter, setDepartmentFilter] = useState("All")
  const [dateFilter, setDateFilter] = useState<Date | undefined>(new Date())
  const [selectedAppointment, setSelectedAppointment] = useState<(typeof appointments)[0] | null>(null)
  const [showNewAppointment, setShowNewAppointment] = useState(false)
  const [showEditAppointment, setShowEditAppointment] = useState(false)
  const [editingAppointment, setEditingAppointment] = useState<(typeof appointments)[0] | null>(null)

  const filteredAppointments = appointments.filter((appointment) => {
    const matchesSearch =
      appointment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      appointment.doctorName.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "All" || appointment.status === statusFilter
    const matchesDepartment = departmentFilter === "All" || appointment.department === departmentFilter

    return matchesSearch && matchesStatus && matchesDepartment
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Appointment Management</h1>
              <p className="text-gray-600 dark:text-gray-400">Schedule, manage, and track patient appointments</p>
            </div>
            <div className="flex gap-3 mt-4 sm:mt-0">
              <Button variant="outline" className="border-gray-200 dark:border-gray-700 bg-transparent">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Dialog open={showNewAppointment} onOpenChange={setShowNewAppointment}>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    New Appointment
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>New Appointment</DialogTitle>
                    <DialogDescription>Schedule a new patient appointment</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newPatient">Patient</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select patient" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="p001">John Smith (P001)</SelectItem>
                            <SelectItem value="p002">Sarah Johnson (P002)</SelectItem>
                            <SelectItem value="p003">Michael Brown (P003)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newDoctor">Doctor</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select doctor" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="dr1">Dr. Sarah Williams</SelectItem>
                            <SelectItem value="dr2">Dr. Emily Rodriguez</SelectItem>
                            <SelectItem value="dr3">Dr. James Thompson</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newDate">Date</Label>
                        <Input id="newDate" type="date" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newTime">Time</Label>
                        <Input id="newTime" type="time" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="newType">Appointment Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="consultation">Consultation</SelectItem>
                            <SelectItem value="followup">Follow-up</SelectItem>
                            <SelectItem value="emergency">Emergency</SelectItem>
                            <SelectItem value="newpatient">New Patient</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="newDuration">Duration</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select duration" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="30">30 minutes</SelectItem>
                            <SelectItem value="45">45 minutes</SelectItem>
                            <SelectItem value="60">60 minutes</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newReason">Reason</Label>
                      <Textarea id="newReason" placeholder="Reason for appointment" />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowNewAppointment(false)}>
                      Cancel
                    </Button>
                    <Button
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={() => {
                        // Create appointment logic here
                        setShowNewAppointment(false)
                      }}
                    >
                      Create Appointment
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-6">
            {/* Calendar Sidebar */}
            <div className="lg:col-span-1">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
                <CardHeader>
                  <CardTitle className="text-lg">Calendar</CardTitle>
                </CardHeader>
                <CardContent>
                  <Calendar
                    mode="single"
                    selected={dateFilter}
                    onSelect={setDateFilter}
                    className="rounded-md border-0"
                  />
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="text-lg">Today's Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Total</span>
                    <span className="font-semibold text-gray-900 dark:text-white">24</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Confirmed</span>
                    <span className="font-semibold text-green-600">18</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Pending</span>
                    <span className="font-semibold text-yellow-600">4</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Cancelled</span>
                    <span className="font-semibold text-red-600">2</span>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Appointments List */}
            <div className="lg:col-span-3">
              {/* Filters */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800 mb-6">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row gap-4">
                    <div className="flex-1 relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search appointments..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 border-gray-200 dark:border-gray-700"
                      />
                    </div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full lg:w-48">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All">All Status</SelectItem>
                        <SelectItem value="Confirmed">Confirmed</SelectItem>
                        <SelectItem value="Pending">Pending</SelectItem>
                        <SelectItem value="Completed">Completed</SelectItem>
                        <SelectItem value="Cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                      <SelectTrigger className="w-full lg:w-48">
                        <SelectValue placeholder="Filter by department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All">All Departments</SelectItem>
                        <SelectItem value="Cardiology">Cardiology</SelectItem>
                        <SelectItem value="Neurology">Neurology</SelectItem>
                        <SelectItem value="Pediatrics">Pediatrics</SelectItem>
                        <SelectItem value="Orthopedics">Orthopedics</SelectItem>
                        <SelectItem value="Dermatology">Dermatology</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Appointments Grid */}
              <div className="space-y-4">
                {filteredAppointments.map((appointment, index) => (
                  <motion.div
                    key={appointment.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                  >
                    <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                      <CardContent className="p-6">
                        <div className="flex flex-col lg:flex-row gap-4">
                          {/* Time & Status */}
                          <div className="flex flex-col items-start lg:items-center lg:w-32">
                            <div className="text-2xl font-bold text-gray-900 dark:text-white">{appointment.time}</div>
                            <div className="text-sm text-gray-600 dark:text-gray-400">{appointment.duration}</div>
                            <Badge className={`mt-2 ${statusColors[appointment.status as keyof typeof statusColors]}`}>
                              {appointment.status}
                            </Badge>
                          </div>

                          {/* Appointment Details */}
                          <div className="flex-1 space-y-3">
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                              <div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                                  {appointment.patientName}
                                </h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                  {appointment.patientId} • {appointment.phone}
                                </p>
                              </div>
                              <Badge
                                className={`${typeColors[appointment.type as keyof typeof typeColors]} mt-2 sm:mt-0`}
                              >
                                {appointment.type}
                              </Badge>
                            </div>

                            <div className="grid sm:grid-cols-2 gap-3 text-sm">
                              <div className="flex items-center text-gray-600 dark:text-gray-400">
                                <Stethoscope className="h-4 w-4 mr-2" />
                                {appointment.doctorName}
                              </div>
                              <div className="flex items-center text-gray-600 dark:text-gray-400">
                                <CalendarIcon className="h-4 w-4 mr-2" />
                                {appointment.department}
                              </div>
                            </div>

                            <div className="text-sm text-gray-700 dark:text-gray-300">
                              <strong>Reason:</strong> {appointment.reason}
                            </div>

                            {appointment.notes && (
                              <div className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                                <strong>Notes:</strong> {appointment.notes}
                              </div>
                            )}
                          </div>

                          {/* Actions */}
                          <div className="flex flex-row lg:flex-col gap-2 lg:w-32">
                            <Button
                              size="sm"
                              onClick={() => setSelectedAppointment(appointment)}
                              className="bg-blue-600 hover:bg-blue-700 text-white flex-1 lg:flex-none"
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-gray-200 dark:border-gray-700 flex-1 lg:flex-none bg-transparent"
                              onClick={() => {
                                setEditingAppointment(appointment)
                                setShowEditAppointment(true)
                              }}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            {appointment.status === "Pending" && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="border-green-200 text-green-600 hover:bg-green-50 dark:border-green-800 dark:text-green-400 flex-1 lg:flex-none bg-transparent"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Confirm
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </main>
      </div>

      {/* Appointment Detail Modal */}
      {selectedAppointment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Appointment Details</h2>
                  <p className="text-gray-600 dark:text-gray-400">{selectedAppointment.id}</p>
                </div>
                <Button
                  variant="ghost"
                  onClick={() => setSelectedAppointment(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Patient Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Name:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.patientName}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Patient ID:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.patientId}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Phone:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.phone}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Email:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.email}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-gray-200 dark:border-gray-700">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <CalendarIcon className="h-5 w-5 mr-2" />
                      Appointment Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Date:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.date}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Time:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.time}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Duration:</span>
                      <span className="ml-2 font-medium">{selectedAppointment.duration}</span>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Type:</span>
                      <Badge className={`ml-2 ${typeColors[selectedAppointment.type as keyof typeof typeColors]}`}>
                        {selectedAppointment.type}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      <Badge
                        className={`ml-2 ${statusColors[selectedAppointment.status as keyof typeof statusColors]}`}
                      >
                        {selectedAppointment.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Stethoscope className="h-5 w-5 mr-2" />
                    Medical Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Doctor:</span>
                    <span className="ml-2 font-medium">{selectedAppointment.doctorName}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Department:</span>
                    <span className="ml-2 font-medium">{selectedAppointment.department}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Reason:</span>
                    <p className="mt-1 text-gray-900 dark:text-white">{selectedAppointment.reason}</p>
                  </div>
                  {selectedAppointment.notes && (
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Notes:</span>
                      <p className="mt-1 text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                        {selectedAppointment.notes}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="flex gap-4 pt-4">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Appointment
                </Button>
                {selectedAppointment.status === "Pending" && (
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Confirm
                  </Button>
                )}
                <Button variant="outline" className="border-red-200 text-red-600 hover:bg-red-50 bg-transparent">
                  <XCircle className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Edit Appointment Modal */}
      {showEditAppointment && editingAppointment && (
        <Dialog open={showEditAppointment} onOpenChange={setShowEditAppointment}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Edit Appointment - {editingAppointment.id}</DialogTitle>
              <DialogDescription>Update appointment details</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editDate">Date</Label>
                  <Input id="editDate" type="date" defaultValue={editingAppointment.date} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editTime">Time</Label>
                  <Input id="editTime" type="time" defaultValue={editingAppointment.time} />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="editStatus">Status</Label>
                  <Select defaultValue={editingAppointment.status}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Confirmed">Confirmed</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="Completed">Completed</SelectItem>
                      <SelectItem value="Cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="editType">Type</Label>
                  <Select defaultValue={editingAppointment.type}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="New Patient">New Patient</SelectItem>
                      <SelectItem value="Follow-up">Follow-up</SelectItem>
                      <SelectItem value="Consultation">Consultation</SelectItem>
                      <SelectItem value="Emergency">Emergency</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="editReason">Reason</Label>
                <Textarea id="editReason" defaultValue={editingAppointment.reason} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="editNotes">Notes</Label>
                <Textarea id="editNotes" defaultValue={editingAppointment.notes || ""} />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEditAppointment(false)}>
                Cancel
              </Button>
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  // Update appointment logic here
                  setShowEditAppointment(false)
                }}
              >
                Update Appointment
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

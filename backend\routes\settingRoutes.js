const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Get system settings - Coming soon' });
});

router.put('/', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Update system settings - Coming soon' });
});

router.get('/departments', (req, res) => {
  res.json({ success: true, message: 'Get departments - Coming soon' });
});

router.post('/departments', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Add department - Coming soon' });
});

router.put('/departments/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Update department - Coming soon' });
});

router.delete('/departments/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Delete department - Coming soon' });
});

module.exports = router;

const express = require('express');
const {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  getPatientSummary,
  searchPatients,
  getPatientsByDoctor,
  getPatientStatistics
} = require('../controllers/patientController');
const { protect, authorize, authorizeOwnerOrAdmin } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

const router = express.Router();

// Public patient registration route
router.post('/register', validate(schemas.patientRegistration), createPatient);

// Protected routes
router.use(protect);

// Search patients (before /:id routes to avoid conflicts)
router.get('/search', authorize('admin', 'doctor', 'nurse'), searchPatients);

// Statistics
router.get('/statistics', authorize('admin', 'doctor'), getPatientStatistics);

// Doctor's patients
router.get('/doctor/:doctorId', authorize('admin', 'doctor'), getPatientsByDoctor);

// CRUD operations
router.route('/')
  .get(authorize('admin', 'doctor', 'nurse'), getPatients)
  .post(authorize('admin', 'nurse'), validate(schemas.patientRegistration), createPatient);

router.route('/:id')
  .get(authorizeOwnerOrAdmin(), getPatient)
  .put(authorizeOwnerOrAdmin(), updatePatient)
  .delete(authorize('admin'), deletePatient);

// Patient summary
router.get('/:id/summary', authorizeOwnerOrAdmin(), getPatientSummary);

module.exports = router;

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Search, Plus, MapPin, Clock, User, Truck, Navigation, Phone } from "lucide-react"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const ambulances = [
  {
    id: "AMB001",
    vehicleNumber: "EMR-001",
    status: "available",
    location: "Hospital Base",
    driver: "<PERSON> Driver",
    paramedic: "Sarah Medic",
    lastMaintenance: "2024-01-10",
    mileage: "45,230",
    fuelLevel: "85%",
  },
  {
    id: "AMB002",
    vehicleNumber: "EMR-002",
    status: "dispatched",
    location: "En route to 123 Main St",
    driver: "Mike Wilson",
    paramedic: "Lisa Johnson",
    lastMaintenance: "2024-01-08",
    mileage: "38,450",
    fuelLevel: "60%",
  },
  {
    id: "AMB003",
    vehicleNumber: "EMR-003",
    status: "maintenance",
    location: "Service Center",
    driver: "N/A",
    paramedic: "N/A",
    lastMaintenance: "2024-01-15",
    mileage: "52,100",
    fuelLevel: "N/A",
  },
]

const dispatches = [
  {
    id: "DISP001",
    ambulanceId: "AMB002",
    priority: "high",
    location: "123 Main Street",
    patientName: "Emergency Call",
    dispatchTime: "14:30",
    estimatedArrival: "14:45",
    status: "en-route",
  },
  {
    id: "DISP002",
    ambulanceId: "AMB001",
    priority: "medium",
    location: "456 Oak Avenue",
    patientName: "Medical Transport",
    dispatchTime: "13:15",
    estimatedArrival: "13:30",
    status: "completed",
  },
]

const stats = [
  { title: "Available Ambulances", value: "1", icon: Truck, color: "text-green-600" },
  { title: "Active Dispatches", value: "1", icon: Navigation, color: "text-blue-600" },
  { title: "Avg Response Time", value: "8.5 min", icon: Clock, color: "text-purple-600" },
  { title: "Today's Calls", value: "15", icon: Phone, color: "text-orange-600" },
]

export default function AmbulancePage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "dispatched":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
      case "maintenance":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "out-of-service":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const filteredAmbulances = ambulances.filter((ambulance) => {
    const matchesSearch =
      ambulance.vehicleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulance.driver.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ambulance.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || ambulance.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Ambulance Management</h1>
                <p className="text-muted-foreground">Manage ambulance fleet and dispatch operations</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="mr-2 h-4 w-4" />
                    New Dispatch
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>New Ambulance Dispatch</DialogTitle>
                    <DialogDescription>Dispatch an ambulance for emergency or transport</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="ambulance">Select Ambulance</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select ambulance" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="amb001">EMR-001 (Available)</SelectItem>
                            <SelectItem value="amb003">EMR-003 (Available)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="priority">Priority Level</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="high">High</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="low">Low</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">Destination Address</Label>
                      <Input placeholder="Enter full address" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="patientName">Patient/Contact Name</Label>
                        <Input placeholder="Enter name" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="contactPhone">Contact Phone</Label>
                        <Input placeholder="+1-************" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="notes">Additional Notes</Label>
                      <Textarea placeholder="Special instructions or medical information" />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">Cancel</Button>
                    <Button className="bg-blue-600 hover:bg-blue-700">Dispatch Ambulance</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {stats.map((stat) => {
                const Icon = stat.icon
                return (
                  <Card key={stat.title} className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                      <Icon className={`h-4 w-4 ${stat.color}`} />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stat.value}</div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <Tabs defaultValue="fleet" className="space-y-4">
              <TabsList>
                <TabsTrigger value="fleet">Fleet Status</TabsTrigger>
                <TabsTrigger value="dispatches">Active Dispatches</TabsTrigger>
                <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>

              <TabsContent value="fleet" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardHeader>
                    <CardTitle>Filter Ambulances</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search by vehicle number, driver, or ID..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="available">Available</SelectItem>
                          <SelectItem value="dispatched">Dispatched</SelectItem>
                          <SelectItem value="maintenance">Maintenance</SelectItem>
                          <SelectItem value="out-of-service">Out of Service</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Ambulance List */}
                <div className="grid gap-4">
                  {filteredAmbulances.map((ambulance) => (
                    <Card key={ambulance.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold text-lg">{ambulance.vehicleNumber}</h3>
                              <Badge className={getStatusColor(ambulance.status)}>{ambulance.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <MapPin className="h-4 w-4 mr-2" />
                                {ambulance.location}
                              </div>
                              <div className="flex items-center">
                                <User className="h-4 w-4 mr-2" />
                                Driver: {ambulance.driver}
                              </div>
                              <div className="flex items-center">
                                <User className="h-4 w-4 mr-2" />
                                Paramedic: {ambulance.paramedic}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-2" />
                                Last Service: {ambulance.lastMaintenance}
                              </div>
                            </div>
                            <div className="flex items-center space-x-4 text-sm">
                              <span>Mileage: {ambulance.mileage} km</span>
                              <span>Fuel: {ambulance.fuelLevel}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              Track Location
                            </Button>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              View Details
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="dispatches" className="space-y-4">
                <div className="grid gap-4">
                  {dispatches.map((dispatch) => (
                    <Card key={dispatch.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold text-lg">Dispatch #{dispatch.id}</h3>
                              <Badge className={getPriorityColor(dispatch.priority)}>
                                {dispatch.priority} priority
                              </Badge>
                              <Badge className={getStatusColor(dispatch.status)}>{dispatch.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <Truck className="h-4 w-4 mr-2" />
                                Ambulance: {dispatch.ambulanceId}
                              </div>
                              <div className="flex items-center">
                                <MapPin className="h-4 w-4 mr-2" />
                                {dispatch.location}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-2" />
                                Dispatched: {dispatch.dispatchTime}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-2" />
                                ETA: {dispatch.estimatedArrival}
                              </div>
                            </div>
                            <p className="text-sm">Patient: {dispatch.patientName}</p>
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              Contact Crew
                            </Button>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              Update Status
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="maintenance">
                <Card>
                  <CardHeader>
                    <CardTitle>Maintenance Schedule</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Maintenance scheduling and tracking would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Dispatch History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Historical dispatch data would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

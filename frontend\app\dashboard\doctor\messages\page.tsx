"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Search, Plus, Send, MessageSquare, Clock, Users, Star, Archive, Trash2 } from "lucide-react"
import { motion } from "framer-motion"
import { Doctor<PERSON>idebar } from "@/components/doctor-sidebar"
import { DoctorHeader } from "@/components/doctor-header"

const messages = [
  {
    id: "MSG001",
    from: "<PERSON>",
    fromId: "P001",
    fromType: "patient",
    subject: "Follow-up Question About Medication",
    message:
      "Hi Dr. <PERSON>, I've been taking the Lisinopril for a week now and I'm experiencing some dizziness. Is this normal? Should I continue taking it?",
    timestamp: "2024-01-20 14:30",
    status: "unread",
    priority: "normal",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "MSG002",
    from: "Sarah Johnson",
    fromId: "P002",
    fromType: "patient",
    subject: "Blood Sugar Readings",
    message:
      "Doctor, my morning blood sugar readings have been consistently around 140-150 mg/dL. Should I be concerned? I've been following the diet plan you gave me.",
    timestamp: "2024-01-20 10:15",
    status: "unread",
    priority: "high",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "MSG003",
    from: "Dr. Michael Chen",
    fromId: "D002",
    fromType: "doctor",
    subject: "Patient Referral - Michael Brown",
    message:
      "Hi Sarah, I'm referring Michael Brown to you for cardiac evaluation. He's been experiencing chest pain on exertion. His recent ECG shows some abnormalities. Please see attached reports.",
    timestamp: "2024-01-19 16:45",
    status: "read",
    priority: "urgent",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "MSG004",
    from: "Emily Davis",
    fromId: "P004",
    fromType: "patient",
    subject: "Appointment Rescheduling",
    message:
      "Hi Dr. Williams, I need to reschedule my appointment next week due to a work conflict. Are there any available slots on Friday afternoon?",
    timestamp: "2024-01-19 09:20",
    status: "read",
    priority: "normal",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "MSG005",
    from: "Nurse Jennifer",
    fromId: "N001",
    fromType: "staff",
    subject: "Lab Results Ready",
    message:
      "Dr. Williams, the lab results for John Smith are ready for review. His cholesterol levels show improvement since starting the statin therapy.",
    timestamp: "2024-01-18 11:30",
    status: "read",
    priority: "normal",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const unreadMessages = messages.filter((msg) => msg.status === "unread")
const urgentMessages = messages.filter((msg) => msg.priority === "urgent" || msg.priority === "high")

export default function DoctorMessagesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedMessage, setSelectedMessage] = useState(null)
  const [replyText, setReplyText] = useState("")

  const filteredMessages = messages.filter(
    (message) =>
      message.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.message.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  const handleReply = (messageId: string) => {
    // Handle reply logic here
    console.log(`Replying to message ${messageId}: ${replyText}`)
    setReplyText("")
  }

  const markAsRead = (messageId: string) => {
    // Handle mark as read logic here
    console.log(`Marking message ${messageId} as read`)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "patient":
        return <Users className="h-4 w-4" />
      case "doctor":
        return <Star className="h-4 w-4" />
      case "staff":
        return <MessageSquare className="h-4 w-4" />
      default:
        return <MessageSquare className="h-4 w-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Messages</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Communicate with patients, colleagues, and staff members
              </p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700 text-white">
              <Plus className="h-4 w-4 mr-2" />
              New Message
            </Button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Messages</p>
                    <p className="text-3xl font-bold text-gray-900 dark:text-white">{messages.length}</p>
                  </div>
                  <MessageSquare className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Unread</p>
                    <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">{unreadMessages.length}</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Urgent</p>
                    <p className="text-3xl font-bold text-red-600 dark:text-red-400">{urgentMessages.length}</p>
                  </div>
                  <Star className="h-8 w-8 text-red-600 dark:text-red-400" />
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Today</p>
                    <p className="text-3xl font-bold text-green-600 dark:text-green-400">3</p>
                  </div>
                  <MessageSquare className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Messages List */}
            <div className="lg:col-span-2">
              <Tabs defaultValue="all" className="space-y-6">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="all">All Messages ({messages.length})</TabsTrigger>
                  <TabsTrigger value="unread">Unread ({unreadMessages.length})</TabsTrigger>
                  <TabsTrigger value="urgent">Urgent ({urgentMessages.length})</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="space-y-6">
                  {/* Search */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardContent className="p-6">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          placeholder="Search messages by sender, subject, or content..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Messages List */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">All Messages</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Your complete message inbox
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {filteredMessages.map((message, index) => (
                          <motion.div
                            key={message.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.4, delay: index * 0.1 }}
                            className={`p-4 rounded-lg border cursor-pointer transition-all duration-300 hover:shadow-md ${
                              message.status === "unread"
                                ? "border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950/20"
                                : "border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-800"
                            } ${selectedMessage?.id === message.id ? "ring-2 ring-blue-500" : ""}`}
                            onClick={() => setSelectedMessage(message)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex items-start space-x-4 flex-1">
                                <Avatar className="h-10 w-10">
                                  <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.from} />
                                  <AvatarFallback>
                                    {message.from
                                      .split(" ")
                                      .map((n) => n[0])
                                      .join("")}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <p
                                      className={`font-medium truncate ${
                                        message.status === "unread"
                                          ? "text-gray-900 dark:text-white"
                                          : "text-gray-700 dark:text-gray-300"
                                      }`}
                                    >
                                      {message.from}
                                    </p>
                                    <div className="flex items-center space-x-1">
                                      {getTypeIcon(message.fromType)}
                                      <Badge variant="secondary" className="text-xs">
                                        {message.fromType}
                                      </Badge>
                                    </div>
                                  </div>
                                  <p
                                    className={`text-sm mb-2 ${
                                      message.status === "unread"
                                        ? "font-medium text-gray-900 dark:text-white"
                                        : "text-gray-600 dark:text-gray-400"
                                    }`}
                                  >
                                    {message.subject}
                                  </p>
                                  <p className="text-sm text-gray-500 dark:text-gray-500 line-clamp-2">
                                    {message.message}
                                  </p>
                                  <div className="flex items-center justify-between mt-2">
                                    <p className="text-xs text-gray-400 dark:text-gray-500">{message.timestamp}</p>
                                    <div className="flex items-center space-x-2">
                                      <Badge className={getPriorityColor(message.priority)}>{message.priority}</Badge>
                                      {message.status === "unread" && (
                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="unread" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Unread Messages</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        Messages requiring your attention
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {unreadMessages.map((message, index) => (
                          <div
                            key={message.id}
                            className="p-4 rounded-lg border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20 cursor-pointer"
                            onClick={() => setSelectedMessage(message)}
                          >
                            <div className="flex items-start space-x-4">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.from} />
                                <AvatarFallback>
                                  {message.from
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <p className="font-medium text-gray-900 dark:text-white">{message.from}</p>
                                  <Badge className={getPriorityColor(message.priority)}>{message.priority}</Badge>
                                </div>
                                <p className="font-medium text-sm text-gray-900 dark:text-white mb-1">
                                  {message.subject}
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                  {message.message}
                                </p>
                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">{message.timestamp}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="urgent" className="space-y-6">
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Urgent Messages</CardTitle>
                      <CardDescription className="text-gray-600 dark:text-gray-400">
                        High priority messages requiring immediate attention
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {urgentMessages.map((message, index) => (
                          <div
                            key={message.id}
                            className="p-4 rounded-lg border border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20 cursor-pointer"
                            onClick={() => setSelectedMessage(message)}
                          >
                            <div className="flex items-start space-x-4">
                              <Avatar className="h-10 w-10">
                                <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.from} />
                                <AvatarFallback>
                                  {message.from
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-2">
                                  <p className="font-medium text-gray-900 dark:text-white">{message.from}</p>
                                  <Badge className={getPriorityColor(message.priority)}>{message.priority}</Badge>
                                </div>
                                <p className="font-medium text-sm text-gray-900 dark:text-white mb-1">
                                  {message.subject}
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                                  {message.message}
                                </p>
                                <p className="text-xs text-red-600 dark:text-red-400 mt-2">{message.timestamp}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>

            {/* Message Detail & Reply */}
            <div className="space-y-6">
              {selectedMessage ? (
                <>
                  {/* Message Detail */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-gray-900 dark:text-white">Message Details</CardTitle>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline">
                            <Archive className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage
                              src={selectedMessage.avatar || "/placeholder.svg"}
                              alt={selectedMessage.from}
                            />
                            <AvatarFallback>
                              {selectedMessage.from
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{selectedMessage.from}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{selectedMessage.fromType}</p>
                            <p className="text-xs text-gray-400 dark:text-gray-500">{selectedMessage.timestamp}</p>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white mb-2">{selectedMessage.subject}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                            {selectedMessage.message}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge className={getPriorityColor(selectedMessage.priority)}>
                            {selectedMessage.priority}
                          </Badge>
                          <Badge
                            variant="secondary"
                            className={
                              selectedMessage.status === "unread"
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"
                            }
                          >
                            {selectedMessage.status}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Reply */}
                  <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                    <CardHeader>
                      <CardTitle className="text-gray-900 dark:text-white">Reply</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <Textarea
                          placeholder="Type your reply..."
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          rows={4}
                        />
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => markAsRead(selectedMessage.id)}
                              className="bg-transparent"
                            >
                              Mark as Read
                            </Button>
                          </div>
                          <Button
                            onClick={() => handleReply(selectedMessage.id)}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            <Send className="h-4 w-4 mr-2" />
                            Send Reply
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                  <CardContent className="p-12 text-center">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 dark:text-gray-400">Select a message to view details</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

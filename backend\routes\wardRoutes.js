const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get all wards - Coming soon' });
});

router.get('/:id', authorize('admin', 'nurse'), (req, res) => {
  res.json({ success: true, message: 'Get ward - Coming soon' });
});

router.post('/', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Create ward - Coming soon' });
});

router.put('/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Update ward - Coming soon' });
});

router.delete('/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Delete ward - Coming soon' });
});

module.exports = router;

const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/', (req, res) => {
  res.json({ success: true, message: 'Get all doctors - Coming soon' });
});

router.get('/:id', (req, res) => {
  res.json({ success: true, message: 'Get doctor by ID - Coming soon' });
});

router.get('/specialties', (req, res) => {
  res.json({ success: true, message: 'Get specialties - Coming soon' });
});

// Protected routes
router.use(protect);

router.post('/', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Create doctor - Coming soon' });
});

router.put('/:id', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Update doctor - Coming soon' });
});

router.delete('/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Delete doctor - Coming soon' });
});

router.get('/:id/schedule', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get doctor schedule - Coming soon' });
});

router.put('/:id/schedule', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Update doctor schedule - Coming soon' });
});

router.get('/:id/patients', authorize('admin', 'doctor'), (req, res) => {
  res.json({ success: true, message: 'Get doctor patients - Coming soon' });
});

module.exports = router;

"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>riangle, MapPin, Clock, User, Activity, Zap, Shield, Search, Plus } from "lucide-react"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { Dash<PERSON>Header } from "@/components/dashboard-header"

const emergencies = [
  {
    id: "EM001",
    type: "Cardiac Arrest",
    priority: "critical",
    patient: "<PERSON>",
    age: 45,
    location: "Room 302",
    reportedBy: "Nurse <PERSON>",
    reportedAt: "2024-01-15 14:30",
    status: "active",
    assignedTeam: "Code Blue Team",
    vitals: { bp: "80/40", hr: "45", temp: "98.6°F" },
  },
  {
    id: "EM002",
    type: "Severe Trauma",
    priority: "high",
    patient: "Jane Doe",
    age: 28,
    location: "Emergency Room 1",
    reportedBy: "Dr. Wilson",
    reportedAt: "2024-01-15 14:15",
    status: "responding",
    assignedTeam: "Trauma Team A",
    vitals: { bp: "90/60", hr: "110", temp: "99.2°F" },
  },
  {
    id: "EM003",
    type: "Respiratory Distress",
    priority: "medium",
    patient: "Mike Johnson",
    age: 67,
    location: "ICU Bed 5",
    reportedBy: "Dr. Brown",
    reportedAt: "2024-01-15 13:45",
    status: "resolved",
    assignedTeam: "ICU Team",
    vitals: { bp: "120/80", hr: "85", temp: "98.4°F" },
  },
]

const emergencyTeams = [
  { name: "Code Blue Team", status: "available", members: 5, leader: "Dr. Smith" },
  { name: "Trauma Team A", status: "busy", members: 6, leader: "Dr. Johnson" },
  { name: "Trauma Team B", status: "available", members: 4, leader: "Dr. Wilson" },
  { name: "ICU Team", status: "available", members: 8, leader: "Dr. Brown" },
]

const stats = [
  { title: "Active Emergencies", value: "2", icon: AlertTriangle, color: "text-red-600" },
  { title: "Response Teams", value: "4", icon: Shield, color: "text-blue-600" },
  { title: "Avg Response Time", value: "3.2 min", icon: Clock, color: "text-green-600" },
  { title: "Today's Cases", value: "12", icon: Activity, color: "text-purple-600" },
]

export default function EmergencyPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "critical":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300"
      case "medium":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      case "responding":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "resolved":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const getTeamStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "busy":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      case "off-duty":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const filteredEmergencies = emergencies.filter((emergency) => {
    const matchesSearch =
      emergency.patient.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emergency.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emergency.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPriority = priorityFilter === "all" || emergency.priority === priorityFilter
    const matchesStatus = statusFilter === "all" || emergency.status === statusFilter
    return matchesSearch && matchesPriority && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Emergency Management</h1>
                <p className="text-muted-foreground">Monitor and manage emergency situations</p>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" className="bg-red-50 border-red-200 text-red-700 hover:bg-red-100">
                  <Zap className="mr-2 h-4 w-4" />
                  Emergency Alert
                </Button>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="mr-2 h-4 w-4" />
                      Report Emergency
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Report New Emergency</DialogTitle>
                      <DialogDescription>Report a new emergency situation</DialogDescription>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="emergencyType">Emergency Type</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="Select emergency type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="cardiac">Cardiac Arrest</SelectItem>
                              <SelectItem value="trauma">Severe Trauma</SelectItem>
                              <SelectItem value="respiratory">Respiratory Distress</SelectItem>
                              <SelectItem value="stroke">Stroke</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="priority">Priority Level</Label>
                          <Select>
                            <SelectTrigger>
                              <SelectValue placeholder="Select priority" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="critical">Critical</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="low">Low</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="patient">Patient Name</Label>
                          <Input placeholder="Enter patient name" />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="location">Location</Label>
                          <Input placeholder="Room/Ward/Department" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea placeholder="Describe the emergency situation" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="team">Assign Team</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select response team" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="code-blue">Code Blue Team</SelectItem>
                            <SelectItem value="trauma-a">Trauma Team A</SelectItem>
                            <SelectItem value="trauma-b">Trauma Team B</SelectItem>
                            <SelectItem value="icu">ICU Team</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button variant="outline">Cancel</Button>
                      <Button className="bg-red-600 hover:bg-red-700">Report Emergency</Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>

            {/* Active Emergency Alert */}
            <Alert className="border-red-200 bg-red-50 dark:bg-red-900/20">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800 dark:text-red-200">
                <strong>Active Emergency:</strong> Cardiac Arrest in Room 302 - Code Blue Team responding
              </AlertDescription>
            </Alert>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {stats.map((stat) => {
                const Icon = stat.icon
                return (
                  <Card key={stat.title} className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                      <Icon className={`h-4 w-4 ${stat.color}`} />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stat.value}</div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <Tabs defaultValue="emergencies" className="space-y-4">
              <TabsList>
                <TabsTrigger value="emergencies">Active Emergencies</TabsTrigger>
                <TabsTrigger value="teams">Response Teams</TabsTrigger>
                <TabsTrigger value="protocols">Protocols</TabsTrigger>
                <TabsTrigger value="history">History</TabsTrigger>
              </TabsList>

              <TabsContent value="emergencies" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardHeader>
                    <CardTitle>Filter Emergencies</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search by patient, type, or ID..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Priorities</SelectItem>
                          <SelectItem value="critical">Critical</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="responding">Responding</SelectItem>
                          <SelectItem value="resolved">Resolved</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Emergency List */}
                <div className="grid gap-4">
                  {filteredEmergencies.map((emergency) => (
                    <Card key={emergency.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <h3 className="font-semibold text-lg">{emergency.type}</h3>
                              <Badge className={getPriorityColor(emergency.priority)}>{emergency.priority}</Badge>
                              <Badge className={getStatusColor(emergency.status)}>{emergency.status}</Badge>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <User className="h-4 w-4 mr-2" />
                                {emergency.patient} (Age: {emergency.age})
                              </div>
                              <div className="flex items-center">
                                <MapPin className="h-4 w-4 mr-2" />
                                {emergency.location}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-2" />
                                Reported: {emergency.reportedAt}
                              </div>
                              <div className="flex items-center">
                                <Shield className="h-4 w-4 mr-2" />
                                Team: {emergency.assignedTeam}
                              </div>
                            </div>
                            <div className="flex items-center space-x-4 text-sm">
                              <span>Vitals:</span>
                              <span>BP: {emergency.vitals.bp}</span>
                              <span>HR: {emergency.vitals.hr}</span>
                              <span>Temp: {emergency.vitals.temp}</span>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                              Update
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="teams" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  {emergencyTeams.map((team) => (
                    <Card key={team.name} className="hover:shadow-md transition-shadow">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{team.name}</CardTitle>
                          <Badge className={getTeamStatusColor(team.status)}>{team.status}</Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Team Leader:</span>
                            <span className="font-medium">{team.leader}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Members:</span>
                            <span className="font-medium">{team.members}</span>
                          </div>
                        </div>
                        <Button variant="outline" className="w-full mt-4 bg-transparent">
                          View Team Details
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="protocols">
                <Card>
                  <CardHeader>
                    <CardTitle>Emergency Protocols</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Emergency protocols and procedures would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Emergency History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Historical emergency data would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

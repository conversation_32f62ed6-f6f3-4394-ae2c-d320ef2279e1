"use client"
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Calendar, Clock, FileText, TrendingUp, AlertCircle, Plus, Eye } from "lucide-react"
import { motion } from "framer-motion"
import { Doctor<PERSON>idebar } from "@/components/doctor-sidebar"
import { <PERSON>Head<PERSON> } from "@/components/doctor-header"

const stats = [
  {
    title: "My Patients",
    value: "24",
    change: "+2 this week",
    trend: "up",
    icon: Users,
    color: "text-blue-600 dark:text-blue-400",
  },
  {
    title: "Today's Appointments",
    value: "8",
    change: "2 pending",
    trend: "up",
    icon: Calendar,
    color: "text-green-600 dark:text-green-400",
  },
  {
    title: "Pending Reviews",
    value: "12",
    change: "3 urgent",
    trend: "up",
    icon: FileText,
    color: "text-orange-600 dark:text-orange-400",
  },
  {
    title: "This Month",
    value: "156",
    change: "+12% from last month",
    trend: "up",
    icon: TrendingUp,
    color: "text-purple-600 dark:text-purple-400",
  },
]

const todayAppointments = [
  { time: "09:00 AM", patient: "<PERSON>", type: "Consultation", status: "confirmed" },
  { time: "10:30 AM", patient: "Sarah Johnson", type: "Follow-up", status: "confirmed" },
  { time: "11:00 AM", patient: "<PERSON>", type: "Check-up", status: "pending" },
  { time: "02:00 PM", patient: "Emily Davis", type: "Consultation", status: "confirmed" },
  { time: "03:30 PM", patient: "Robert Wilson", type: "Emergency", status: "urgent" },
]

const recentPatients = [
  { id: "P001", name: "John Smith", lastVisit: "2024-01-20", condition: "Hypertension", status: "stable" },
  { id: "P002", name: "Sarah Johnson", lastVisit: "2024-01-19", condition: "Diabetes", status: "monitoring" },
  { id: "P003", name: "Michael Brown", lastVisit: "2024-01-18", condition: "Arthritis", status: "improving" },
  { id: "P004", name: "Emily Davis", lastVisit: "2024-01-17", condition: "Migraine", status: "stable" },
]

const pendingTasks = [
  { task: "Review lab results for John Smith", priority: "high", time: "2 hours ago" },
  { task: "Update prescription for Sarah Johnson", priority: "medium", time: "4 hours ago" },
  { task: "Complete medical report for Michael Brown", priority: "low", time: "1 day ago" },
]

export default function DoctorDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DoctorSidebar />

      <div className="lg:pl-64">
        <DoctorHeader />

        <main className="p-6">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Good morning, Dr. Williams</h1>
            <p className="text-gray-600 dark:text-gray-400">
              You have 8 appointments today and 3 urgent tasks pending.
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{stat.change}</p>
                      </div>
                      <div className={`p-3 rounded-2xl bg-gray-50 dark:bg-gray-700 ${stat.color}`}>
                        <stat.icon className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Today's Appointments */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-gray-900 dark:text-white">Today's Appointments</CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-400">
                      Your scheduled appointments for today
                    </CardDescription>
                  </div>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Appointment
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {todayAppointments.map((appointment, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{appointment.patient}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {appointment.time} • {appointment.type}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            variant={
                              appointment.status === "confirmed"
                                ? "default"
                                : appointment.status === "urgent"
                                  ? "destructive"
                                  : "secondary"
                            }
                            className={
                              appointment.status === "confirmed"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : appointment.status === "urgent"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                            }
                          >
                            {appointment.status}
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Pending Tasks */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
                    Pending Tasks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {pendingTasks.map((task, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700"
                      >
                        <div
                          className={`w-2 h-2 rounded-full mt-2 ${
                            task.priority === "high"
                              ? "bg-red-500"
                              : task.priority === "medium"
                                ? "bg-orange-500"
                                : "bg-blue-500"
                          }`}
                        />
                        <div className="flex-1">
                          <p className="text-sm text-gray-900 dark:text-white">{task.task}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{task.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Recent Patients */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <Users className="h-5 w-5 mr-2 text-blue-500" />
                    Recent Patients
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentPatients.map((patient, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg border border-gray-100 dark:border-gray-700"
                      >
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white text-sm">{patient.name}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{patient.condition}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{patient.lastVisit}</p>
                        </div>
                        <Badge
                          variant="secondary"
                          className={
                            patient.status === "stable"
                              ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                              : patient.status === "monitoring"
                                ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                                : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                          }
                        >
                          {patient.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

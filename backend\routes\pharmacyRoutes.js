const express = require('express');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

router.use(protect);

router.get('/medications', (req, res) => {
  res.json({ success: true, message: 'Get all medications - Coming soon' });
});

router.get('/medications/:id', (req, res) => {
  res.json({ success: true, message: 'Get medication - Coming soon' });
});

router.post('/medications', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Add medication - Coming soon' });
});

router.put('/medications/:id', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Update medication - Coming soon' });
});

router.delete('/medications/:id', authorize('admin'), (req, res) => {
  res.json({ success: true, message: 'Delete medication - Coming soon' });
});

router.get('/inventory', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Get inventory - Coming soon' });
});

router.put('/inventory/:id', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Update inventory - Coming soon' });
});

router.get('/prescriptions', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Get pharmacy prescriptions - Coming soon' });
});

router.put('/prescriptions/:id/dispense', authorize('admin', 'pharmacist'), (req, res) => {
  res.json({ success: true, message: 'Dispense medication - Coming soon' });
});

module.exports = router;

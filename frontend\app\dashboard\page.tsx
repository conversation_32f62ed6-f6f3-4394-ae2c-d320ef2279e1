"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Users, Calendar, DollarSign, Bed, TrendingUp, AlertCircle, Clock, UserCheck, Plus } from "lucide-react"
import { motion } from "framer-motion"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { DashboardHeader } from "@/components/dashboard-header"

const stats = [
  {
    title: "Total Patients",
    value: "2,847",
    change: "+12%",
    trend: "up",
    icon: Users,
    color: "text-blue-600 dark:text-blue-400",
  },
  {
    title: "Today's Appointments",
    value: "156",
    change: "+8%",
    trend: "up",
    icon: Calendar,
    color: "text-green-600 dark:text-green-400",
  },
  {
    title: "Available Beds",
    value: "23/120",
    change: "-5%",
    trend: "down",
    icon: Bed,
    color: "text-orange-600 dark:text-orange-400",
  },
  {
    title: "Revenue (Month)",
    value: "$284,750",
    change: "+15%",
    trend: "up",
    icon: DollarSign,
    color: "text-purple-600 dark:text-purple-400",
  },
]

const recentPatients = [
  { id: "P001", name: "John Smith", age: 45, department: "Cardiology", status: "In Progress", time: "10:30 AM" },
  { id: "P002", name: "Sarah Johnson", age: 32, department: "Pediatrics", status: "Waiting", time: "11:00 AM" },
  { id: "P003", name: "Michael Brown", age: 58, department: "Orthopedics", status: "Completed", time: "09:45 AM" },
  { id: "P004", name: "Emily Davis", age: 28, department: "Dermatology", status: "In Progress", time: "11:30 AM" },
  { id: "P005", name: "Robert Wilson", age: 67, department: "Neurology", status: "Waiting", time: "12:00 PM" },
]

const upcomingAppointments = [
  { time: "2:00 PM", patient: "Alice Cooper", doctor: "Dr. Smith", department: "Cardiology" },
  { time: "2:30 PM", patient: "Bob Johnson", doctor: "Dr. Williams", department: "Orthopedics" },
  { time: "3:00 PM", patient: "Carol Davis", doctor: "Dr. Brown", department: "Pediatrics" },
  { time: "3:30 PM", patient: "David Miller", doctor: "Dr. Jones", department: "Neurology" },
]

const alerts = [
  { type: "critical", message: "ICU Bed 5 - Patient requires immediate attention", time: "5 min ago" },
  { type: "warning", message: "Pharmacy - Low stock alert for Amoxicillin", time: "15 min ago" },
  { type: "info", message: "New patient registration - Emergency Department", time: "30 min ago" },
]

export default function DashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white dark:bg-gray-800">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.title}</p>
                        <p className="text-3xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
                        <div className="flex items-center mt-2">
                          <TrendingUp
                            className={`h-4 w-4 mr-1 ${stat.trend === "up" ? "text-green-500" : "text-red-500"}`}
                          />
                          <span
                            className={`text-sm font-medium ${stat.trend === "up" ? "text-green-600" : "text-red-600"}`}
                          >
                            {stat.change}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">from last month</span>
                        </div>
                      </div>
                      <div className={`p-3 rounded-2xl bg-gray-50 dark:bg-gray-700 ${stat.color}`}>
                        <stat.icon className="h-8 w-8" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Recent Patients */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle className="text-gray-900 dark:text-white">Recent Patients</CardTitle>
                    <CardDescription className="text-gray-600 dark:text-gray-400">
                      Latest patient activities and status updates
                    </CardDescription>
                  </div>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Patient
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentPatients.map((patient, index) => (
                      <motion.div
                        key={patient.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        className="flex items-center justify-between p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <UserCheck className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">{patient.name}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {patient.id} • Age {patient.age} • {patient.department}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Badge
                            variant={
                              patient.status === "Completed"
                                ? "default"
                                : patient.status === "In Progress"
                                  ? "secondary"
                                  : "outline"
                            }
                            className={
                              patient.status === "Completed"
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : patient.status === "In Progress"
                                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                  : "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                            }
                          >
                            {patient.status}
                          </Badge>
                          <span className="text-sm text-gray-500 dark:text-gray-400">{patient.time}</span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar Content */}
            <div className="space-y-6">
              {/* Alerts */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
                    Alerts & Notifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {alerts.map((alert, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700"
                      >
                        <div
                          className={`w-2 h-2 rounded-full mt-2 ${
                            alert.type === "critical"
                              ? "bg-red-500"
                              : alert.type === "warning"
                                ? "bg-orange-500"
                                : "bg-blue-500"
                          }`}
                        />
                        <div className="flex-1">
                          <p className="text-sm text-gray-900 dark:text-white">{alert.message}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{alert.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Appointments */}
              <Card className="border-0 shadow-lg bg-white dark:bg-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center text-gray-900 dark:text-white">
                    <Clock className="h-5 w-5 mr-2 text-blue-500" />
                    Upcoming Appointments
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {upcomingAppointments.map((appointment, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg border border-gray-100 dark:border-gray-700"
                      >
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white text-sm">{appointment.time}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{appointment.patient}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {appointment.doctor} • {appointment.department}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

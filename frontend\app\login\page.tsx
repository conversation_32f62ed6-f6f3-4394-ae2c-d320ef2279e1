"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Eye, EyeOff, Shield, User, Stethoscope, UserCog, Lock } from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"
import { useRouter } from "next/navigation"

const userRoles = [
  {
    id: "doctor",
    label: "Doctor",
    icon: Stethoscope,
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    route: "/dashboard/doctor",
  },
  {
    id: "patient",
    label: "Patient",
    icon: User,
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    route: "/dashboard/patient",
  },
  {
    id: "nurse",
    label: "Nurse",
    icon: User,
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
    route: "/dashboard",
  },
  {
    id: "admin",
    label: "Administrator",
    icon: UserCog,
    color: "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200",
    route: "/dashboard",
  },
]

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [selectedRole, setSelectedRole] = useState("doctor")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate login process
    setTimeout(() => {
      setIsLoading(false)
      const role = userRoles.find((r) => r.id === selectedRole)
      if (role) {
        router.push(role.route)
      }
    }, 2000)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
          {/* Header */}
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center space-x-2 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-xl">H+</span>
              </div>
              <div>
                <div className="font-bold text-xl text-gray-900 dark:text-white">HealthCare Plus</div>
                <div className="text-xs text-blue-600 dark:text-blue-400">Portal Login</div>
              </div>
            </Link>
          </div>

          <Card className="border-0 shadow-2xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardHeader className="text-center pb-6">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <CardTitle className="text-2xl text-gray-900 dark:text-white">Portal Login</CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-300">
                Select your role and sign in to continue
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <Tabs value={selectedRole} onValueChange={setSelectedRole} className="w-full">
                <TabsList className="grid w-full grid-cols-2 gap-1 bg-gray-100 dark:bg-gray-700 p-1 rounded-lg">
                  <TabsTrigger
                    value="doctor"
                    className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600"
                  >
                    Doctor
                  </TabsTrigger>
                  <TabsTrigger
                    value="patient"
                    className="data-[state=active]:bg-white dark:data-[state=active]:bg-gray-600"
                  >
                    Patient
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="doctor" className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 gap-2">
                    {userRoles
                      .filter((role) => role.id === "doctor")
                      .map((role) => (
                        <Badge key={role.id} className={`${role.color} justify-center py-2`}>
                          <role.icon className="mr-2 h-4 w-4" />
                          {role.label} Portal
                        </Badge>
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="patient" className="space-y-4 mt-6">
                  <div className="grid grid-cols-1 gap-2">
                    {userRoles
                      .filter((role) => role.id === "patient")
                      .map((role) => (
                        <Badge key={role.id} className={`${role.color} justify-center py-2`}>
                          <role.icon className="mr-2 h-4 w-4" />
                          {role.label} Portal
                        </Badge>
                      ))}
                  </div>
                </TabsContent>
              </Tabs>

              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">
                    {selectedRole === "patient" ? "Patient ID or Email" : "Email or Staff ID"}
                  </Label>
                  <Input
                    id="email"
                    type="text"
                    placeholder={
                      selectedRole === "patient" ? "Enter your patient ID or email" : "Enter your email or staff ID"
                    }
                    required
                    className="border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter your password"
                      required
                      className="border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4 text-gray-400" />
                      ) : (
                        <Eye className="h-4 w-4 text-gray-400" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <Link href="/forgot-password" className="text-blue-600 dark:text-blue-400 hover:underline">
                    Forgot password?
                  </Link>
                </div>

                <Button
                  type="submit"
                  className={`w-full text-white shadow-lg hover:shadow-xl transition-all duration-300 ${
                    selectedRole === "patient" ? "bg-green-600 hover:bg-green-700" : "bg-blue-600 hover:bg-blue-700"
                  }`}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Signing in...
                    </div>
                  ) : (
                    <>
                      <Lock className="mr-2 h-4 w-4" />
                      Sign In to {selectedRole === "patient" ? "Patient" : "Doctor"} Portal
                    </>
                  )}
                </Button>
              </form>

              <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                <p>Need help? Contact Support</p>
                <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
              </div>
            </CardContent>
          </Card>

          <div className="text-center mt-6">
            <Link href="/" className="text-blue-600 dark:text-blue-400 hover:underline text-sm">
              ← Back to Public Website
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

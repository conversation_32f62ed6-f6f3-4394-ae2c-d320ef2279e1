"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Search, Plus, Mail, Phone, Users, Clock, Star } from "lucide-react"
import { DashboardSidebar } from "@/components/dashboard-sidebar"
import { Dash<PERSON>Header } from "@/components/dashboard-header"

const doctors = [
  {
    id: "DR001",
    name: "Dr. <PERSON>",
    specialization: "Cardiology",
    department: "Cardiology",
    email: "<EMAIL>",
    phone: "+****************",
    experience: "15 years",
    status: "active",
    rating: 4.8,
    patients: 245,
    schedule: "Mon-Fri 9:00-17:00",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "DR002",
    name: "Dr. Michael Chen",
    specialization: "Neurology",
    department: "Neurology",
    email: "<EMAIL>",
    phone: "+****************",
    experience: "12 years",
    status: "active",
    rating: 4.9,
    patients: 189,
    schedule: "Mon-Wed, Fri 8:00-16:00",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "DR003",
    name: "Dr. Emily Rodriguez",
    specialization: "Pediatrics",
    department: "Pediatrics",
    email: "<EMAIL>",
    phone: "+****************",
    experience: "8 years",
    status: "on-leave",
    rating: 4.7,
    patients: 156,
    schedule: "Tue-Thu 10:00-18:00",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

const departments = [
  { name: "Cardiology", doctors: 8, icon: "❤️" },
  { name: "Neurology", doctors: 6, icon: "🧠" },
  { name: "Pediatrics", doctors: 12, icon: "👶" },
  { name: "Orthopedics", doctors: 10, icon: "🦴" },
]

export default function DoctorsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [departmentFilter, setDepartmentFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
      case "on-leave":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
    }
  }

  const filteredDoctors = doctors.filter((doctor) => {
    const matchesSearch =
      doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.specialization.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doctor.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDepartment = departmentFilter === "all" || doctor.department === departmentFilter
    const matchesStatus = statusFilter === "all" || doctor.status === statusFilter
    return matchesSearch && matchesDepartment && matchesStatus
  })

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <DashboardSidebar />

      <div className="lg:pl-64">
        <DashboardHeader />

        <main className="p-6">
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Doctors Management</h1>
                <p className="text-muted-foreground">Manage doctor profiles, schedules, and assignments</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Doctor
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Doctor</DialogTitle>
                    <DialogDescription>Create a new doctor profile</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name</Label>
                        <Input placeholder="Dr. John Doe" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="specialization">Specialization</Label>
                        <Input placeholder="Cardiology" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="department">Department</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select department" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="cardiology">Cardiology</SelectItem>
                            <SelectItem value="neurology">Neurology</SelectItem>
                            <SelectItem value="pediatrics">Pediatrics</SelectItem>
                            <SelectItem value="orthopedics">Orthopedics</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="experience">Experience</Label>
                        <Input placeholder="10 years" />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input type="email" placeholder="<EMAIL>" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone</Label>
                        <Input placeholder="+****************" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="schedule">Schedule</Label>
                      <Input placeholder="Mon-Fri 9:00-17:00" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bio">Biography</Label>
                      <Textarea placeholder="Doctor's professional background and qualifications" />
                    </div>
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline">Cancel</Button>
                    <Button className="bg-blue-600 hover:bg-blue-700">Add Doctor</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Stats Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {departments.map((dept) => (
                <Card key={dept.name} className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">{dept.name}</CardTitle>
                    <span className="text-2xl">{dept.icon}</span>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">{dept.doctors}</div>
                    <p className="text-xs text-muted-foreground">Active doctors</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Tabs defaultValue="doctors" className="space-y-4">
              <TabsList>
                <TabsTrigger value="doctors">All Doctors</TabsTrigger>
                <TabsTrigger value="schedules">Schedules</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
              </TabsList>

              <TabsContent value="doctors" className="space-y-4">
                {/* Filters */}
                <Card>
                  <CardHeader>
                    <CardTitle>Filter Doctors</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search by name, specialization, or ID..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8"
                          />
                        </div>
                      </div>
                      <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Departments</SelectItem>
                          <SelectItem value="Cardiology">Cardiology</SelectItem>
                          <SelectItem value="Neurology">Neurology</SelectItem>
                          <SelectItem value="Pediatrics">Pediatrics</SelectItem>
                          <SelectItem value="Orthopedics">Orthopedics</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="on-leave">On Leave</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardContent>
                </Card>

                {/* Doctors Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {filteredDoctors.map((doctor) => (
                    <Card key={doctor.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <Avatar className="h-16 w-16">
                            <AvatarImage src={doctor.avatar || "/placeholder.svg"} alt={doctor.name} />
                            <AvatarFallback>
                              {doctor.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <h3 className="font-semibold text-lg">{doctor.name}</h3>
                              <Badge className={getStatusColor(doctor.status)}>{doctor.status}</Badge>
                            </div>
                            <p className="text-sm text-blue-600 font-medium">{doctor.specialization}</p>
                            <p className="text-sm text-muted-foreground">{doctor.department}</p>

                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <Star className="h-4 w-4 text-yellow-400 mr-1" />
                                {doctor.rating}
                              </div>
                              <div className="flex items-center">
                                <Users className="h-4 w-4 mr-1" />
                                {doctor.patients}
                              </div>
                            </div>

                            <div className="space-y-1 text-sm">
                              <div className="flex items-center">
                                <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                                {doctor.email}
                              </div>
                              <div className="flex items-center">
                                <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                                {doctor.phone}
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                                {doctor.schedule}
                              </div>
                            </div>

                            <div className="flex space-x-2 pt-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button variant="outline" size="sm" className="flex-1 bg-transparent">
                                    View Profile
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl">
                                  <DialogHeader>
                                    <DialogTitle>{doctor.name}</DialogTitle>
                                    <DialogDescription>
                                      {doctor.specialization} • {doctor.department}
                                    </DialogDescription>
                                  </DialogHeader>
                                  <div className="grid gap-4 py-4">
                                    <div className="flex items-center space-x-4">
                                      <Avatar className="h-20 w-20">
                                        <AvatarImage src={doctor.avatar || "/placeholder.svg"} alt={doctor.name} />
                                        <AvatarFallback>
                                          {doctor.name
                                            .split(" ")
                                            .map((n) => n[0])
                                            .join("")}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="space-y-1">
                                        <h3 className="text-xl font-semibold">{doctor.name}</h3>
                                        <p className="text-blue-600">{doctor.specialization}</p>
                                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                          <span>Experience: {doctor.experience}</span>
                                          <span>Rating: {doctor.rating}/5</span>
                                        </div>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                      <div>
                                        <Label className="text-sm font-medium">Contact Information</Label>
                                        <div className="space-y-1 text-sm text-muted-foreground">
                                          <p>{doctor.email}</p>
                                          <p>{doctor.phone}</p>
                                        </div>
                                      </div>
                                      <div>
                                        <Label className="text-sm font-medium">Schedule</Label>
                                        <p className="text-sm text-muted-foreground">{doctor.schedule}</p>
                                      </div>
                                    </div>

                                    <div>
                                      <Label className="text-sm font-medium">Statistics</Label>
                                      <div className="grid grid-cols-3 gap-4 mt-2">
                                        <div className="text-center">
                                          <div className="text-2xl font-bold text-blue-600">{doctor.patients}</div>
                                          <div className="text-xs text-muted-foreground">Patients</div>
                                        </div>
                                        <div className="text-center">
                                          <div className="text-2xl font-bold text-green-600">{doctor.rating}</div>
                                          <div className="text-xs text-muted-foreground">Rating</div>
                                        </div>
                                        <div className="text-center">
                                          <div className="text-2xl font-bold text-purple-600">{doctor.experience}</div>
                                          <div className="text-xs text-muted-foreground">Experience</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="flex justify-end space-x-2">
                                    <Button variant="outline">Edit Profile</Button>
                                    <Button className="bg-blue-600 hover:bg-blue-700">View Schedule</Button>
                                  </div>
                                </DialogContent>
                              </Dialog>
                              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                                Edit
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="schedules">
                <Card>
                  <CardHeader>
                    <CardTitle>Doctor Schedules</CardTitle>
                    <CardDescription>Manage doctor schedules and availability</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Schedule management interface would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="performance">
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Analytics</CardTitle>
                    <CardDescription>View doctor performance metrics and analytics</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">Performance analytics interface would go here.</p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  )
}

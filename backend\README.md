# Hospital Management System - Backend API

A comprehensive Hospital Management System backend built with Node.js, Express.js, and MySQL2 following the MVC (Model-View-Controller) pattern.

## 🏥 Features

- **Authentication & Authorization**: JWT-based auth with role-based access control
- **User Management**: Multi-role user system (Ad<PERSON>, Doctor, Nurse, Patient, etc.)
- **Patient Management**: Complete patient registration and profile management
- **Appointment System**: Booking, scheduling, and management
- **Medical Records**: Electronic health records management
- **Prescription Management**: Digital prescription system
- **Laboratory Management**: Lab test ordering and results
- **Billing System**: Patient billing and payment processing
- **Pharmacy Management**: Medication inventory and dispensing
- **Emergency Services**: Emergency case management
- **Ward & Bed Management**: Hospital resource management
- **Reporting & Analytics**: Comprehensive reporting system
- **Real-time Notifications**: Socket.IO integration
- **File Upload**: Document and image management

## 🚀 Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MySQL
- **Database Driver**: MySQL2
- **Authentication**: JWT (JSON Web Tokens)
- **Password Hashing**: bcryptjs
- **Validation**: Joi
- **File Upload**: Multer
- **Email Service**: Nodemailer
- **Real-time Communication**: Socket.IO
- **Security**: Helmet, CORS, Rate Limiting
- **Environment**: dotenv

## 📁 Project Structure

```
backend/
├── config/
│   └── database.js          # Database configuration
├── controllers/             # Route controllers (business logic)
│   ├── authController.js
│   ├── patientController.js
│   └── ...
├── middleware/              # Custom middleware
│   ├── auth.js             # Authentication middleware
│   ├── errorHandler.js     # Error handling
│   ├── notFound.js         # 404 handler
│   └── validation.js       # Request validation
├── models/                  # Data models
│   ├── User.js
│   ├── Patient.js
│   └── ...
├── routes/                  # API routes
│   ├── authRoutes.js
│   ├── patientRoutes.js
│   └── ...
├── scripts/                 # Database scripts
│   ├── migrate.js          # Database migration
│   └── seed.js             # Seed data
├── utils/                   # Utility functions
│   ├── helpers.js          # Helper functions
│   └── emailService.js     # Email utilities
├── uploads/                 # File uploads directory
├── .env.example            # Environment variables template
├── .gitignore              # Git ignore rules
├── package.json            # Dependencies and scripts
├── server.js               # Application entry point
└── README.md               # This file
```

## 🛠️ Installation & Setup

### Prerequisites

- Node.js (v16 or higher)
- MySQL (v8.0 or higher)
- npm or yarn

### 1. Clone the Repository

```bash
git clone <repository-url>
cd backend
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Configuration

Copy the example environment file and configure your settings:

```bash
cp .env.example .env
```

Edit `.env` file with your configuration:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=hms_database
DB_USER=root
DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRE=30d

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=HealthCare Plus

# Other configurations...
```

### 4. Database Setup

Run the database migration to create tables and insert default data:

```bash
npm run migrate
```

### 5. Start the Server

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

The server will start on `http://localhost:5000`

## 📚 API Documentation

### Base URL
```
http://localhost:5000/api
```

### Authentication Endpoints

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| POST | `/auth/register` | Register new user | Public |
| POST | `/auth/login` | User login | Public |
| GET | `/auth/me` | Get current user | Private |
| POST | `/auth/refresh` | Refresh token | Public |
| POST | `/auth/forgot-password` | Request password reset | Public |
| POST | `/auth/reset-password` | Reset password | Public |
| PUT | `/auth/change-password` | Change password | Private |
| POST | `/auth/logout` | Logout user | Private |

### Patient Endpoints

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/patients` | Get all patients | Admin, Doctor, Nurse |
| GET | `/patients/:id` | Get patient by ID | Owner or Admin |
| POST | `/patients` | Create new patient | Admin, Nurse |
| PUT | `/patients/:id` | Update patient | Owner or Admin |
| DELETE | `/patients/:id` | Delete patient | Admin |
| GET | `/patients/search` | Search patients | Admin, Doctor, Nurse |
| GET | `/patients/:id/summary` | Get patient summary | Owner or Admin |

### Other Endpoints

All other endpoints follow similar RESTful patterns. Detailed API documentation will be available via Swagger UI once implemented.

## 🔐 Authentication & Authorization

The API uses JWT (JSON Web Tokens) for authentication and role-based access control.

### User Roles

- **Admin**: Full system access
- **Doctor**: Medical operations, patient management
- **Nurse**: Patient care, basic operations
- **Patient**: Own data access only
- **Pharmacist**: Medication management
- **Lab Technician**: Laboratory operations
- **Receptionist**: Front desk operations

### Protected Routes

Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## 🗄️ Database Schema

The system uses MySQL with the following main tables:

- `users` - System users
- `roles` - User roles
- `patients` - Patient information
- `doctors` - Doctor profiles
- `appointments` - Appointment bookings
- `medical_records` - Patient medical history
- `prescriptions` - Medication prescriptions
- `medications` - Pharmacy inventory
- `departments` - Hospital departments
- And more...

## 🧪 Testing

Run tests (when implemented):

```bash
npm test
```

## 📝 Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run migrate` - Run database migrations
- `npm run seed` - Seed database with sample data
- `npm test` - Run tests

## 🔧 Environment Variables

See `.env.example` for all available environment variables and their descriptions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Note**: This is the backend API for the Hospital Management System. Make sure to also set up the frontend application for a complete system.
